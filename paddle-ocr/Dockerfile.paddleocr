FROM python:3.10-slim-buster

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libgomp1 \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制OCR服务的需求文件
COPY ./paddleocr_requirements.txt /app/

# 安装依赖
RUN pip install --upgrade pip \
    && pip install -i https://mirrors.aliyun.com/pypi/simple --no-cache-dir --progress-bar off -r paddleocr_requirements.txt

# 复制OCR服务代码
COPY . /app/ocr_service

# 设置环境变量
ENV PYTHONPATH=/app

# 启动OCR服务
CMD ["uvicorn", "ocr_service.main:app", "--host", "0.0.0.0", "--port", "8000"]