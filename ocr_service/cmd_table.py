import subprocess
import os
from pathlib import Path
from typing import Optional, Dict, List
import pandas as pd
import json
from io import StringIO

def extract_table_to_markdown(input_path: str, output_dir: str) -> None:
    """使用tabled命令行工具提取表格并保存为markdown格式"""
    cmd = [
        "tabled",
        input_path,
        output_dir,
        "--format",
        "markdown"
    ]
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Error running tabled command: {e}")
        raise

def read_markdown_file(file_path: str) -> Optional[str]:
    """读取markdown文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"Error reading markdown file: {e}")
        return None

def image_to_markdown(image_path: str) -> Optional[str]:
    """
    将图片转换为markdown格式的表格字符串
    
    Args:
        image_path: 图片文件路径
        
    Returns:
        str: markdown格式的表格内容
    """
    # 准备路径
    input_path = os.path.abspath(image_path)
    base_name = Path(input_path).stem
    output_dir = os.path.join(os.path.dirname(input_path), "results")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 提取表格
    extract_table_to_markdown(input_path, output_dir)
    
    # 构建预期的markdown文件路径
    markdown_path = os.path.join(output_dir, base_name, "page0_table0.md")
    
    # 读取markdown内容
    return read_markdown_file(markdown_path)

def markdown_to_json(markdown_content: str) -> Dict[str, List[Dict]]:
    """
    将markdown表格转换为JSON格式
    
    Args:
        markdown_content: markdown格式的表格内容
        
    Returns:
        Dict[str, List[Dict]]: 按柱号组织的数据字典
    """
    # 清理markdown内容
    table_content = markdown_content.split('===\n\n')[1] if '===' in markdown_content else markdown_content
    
    # 使用StringIO读取markdown表格
    df = pd.read_table(
        StringIO(table_content),
        sep='|',
        header=0,
        skipinitialspace=True
    ).dropna(axis=1, how='all')
    
    # 清理列名
    df.columns = [col.strip() for col in df.columns]
    
    # 初始化结果字典和临时存储
    result = {}
    temp_data = []
    current_kz = None
    
    # 遍历每一行
    for _, row in df.iterrows():
        row_dict = {k: str(v).strip() for k, v in row.to_dict().items()}
        height = row_dict['标高']
        kz_number = row_dict['柱编号'].strip()
        
        # 检查是否是新的KZ分组（通过转义斜杠判断）
        if '\\-' in height:
            # 如果已有数据，保存到之前的KZ
            if temp_data:
                if current_kz:
                    result[current_kz] = temp_data
                temp_data = []
            
            # 寻找下一个非空的KZ编号
            next_kz = None
            if kz_number.startswith('KZ'):
                next_kz = kz_number
            else:
                # 向后查找最近的KZ编号
                for future_row in df.iloc[_:].itertuples():
                    future_kz = str(getattr(future_row, '柱编号')).strip()
                    if future_kz.startswith('KZ'):
                        next_kz = future_kz
                        break
            
            if next_kz:
                current_kz = next_kz
        
        # 如果有标高数据，添加到当前分组
        if height:
            data_entry = {
                '标高': height.replace('\\', '').strip(),
                'bxh': row_dict['bxh'].replace('×', 'x').strip(),
                'b1': row_dict['b1'].strip(),
                'b2': row_dict['b2'].strip(),
                'h1': row_dict['h 1'].strip(),
                'h2': row_dict['h2'].strip(),
                '全部纵筋': row_dict['全部纵筋'].strip(),
                '角筋': row_dict['角筋'].strip(),
                'b边一侧中部筋': row_dict['b边一侧 中部筋'].strip(),
                'h边一侧中部筋': row_dict['h 边一倒 中部筋'].strip(),
                '箍筋类型号': row_dict['箍筋类型号'].strip(),
                '箍筋': row_dict['箍筋'].replace('�', 'Φ').strip(),
            }
            temp_data.append(data_entry)
    
    # 保存最后一组数据
    if temp_data and current_kz:
        result[current_kz] = temp_data
    
    return result

def process_image_to_json(image_path: str, save_json: bool = True) -> Optional[Dict]:
    """
    处理图片并生成JSON数据
    
    Args:
        image_path: 图片文件路径
        save_json: 是否保存JSON文件
        
    Returns:
        Dict: 处理后的JSON数据
    """
    try:
        # 转换图片到markdown
        markdown_content = image_to_markdown(image_path)
        
        if not markdown_content:
            print("No markdown content was generated")
            return None
            
        # 转换markdown到JSON
        json_data = markdown_to_json(markdown_content)
        
        # 保存JSON文件
        if save_json:
            output_path = f"{Path(image_path).stem}_data.json"
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
            print(f"\nJSON data has been saved to {output_path}")
        
        return json_data
        
    except Exception as e:
        print(f"Error processing image: {e}")
        return None

def main():
    # 图片路径
    image_path = "/home/<USER>/py_project/surya-test/tablefiles/柱表.jpg"
    
    # 处理图片
    json_data = process_image_to_json(image_path)
    
    # 打印示例数据
    if json_data:
        print("\nExample of processed data:")
        first_kz = next(iter(json_data))
        print(f"\nFirst KZ ({first_kz}):")
        print(json.dumps(json_data[first_kz][0], ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()