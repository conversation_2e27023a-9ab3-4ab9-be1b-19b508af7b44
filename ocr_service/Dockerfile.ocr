FROM nvidia/cuda:11.8.0-cudnn8-runtime-ubuntu22.04

# 设置工作目录和环境变量
WORKDIR /app
ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONPATH=/app \
    TORCH_DEVICE=cuda \
    PYTHONUNBUFFERED=1 \ 
    TRANSFORMERS_OFFLINE=1

# 安装Python和系统依赖
RUN apt-get update && apt-get install -y \
    python3.10 \
    python3-pip \
    python3.10-dev \
    build-essential \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    && rm -rf /var/lib/apt/lists/* \
    && ln -s /usr/bin/python3.10 /usr/bin/python

# 预创建模型缓存目录并设置权限
RUN mkdir -p /root/.cache/huggingface/hub && \
    chmod -R 777 /root/.cache/huggingface/hub

# 复制requirements.txt
COPY ocr_requirements.txt .

# 升级pip并安装依赖
RUN python -m pip install --upgrade pip \
    && pip install -i https://mirrors.aliyun.com/pypi/simple --no-cache-dir --progress-bar off -r ocr_requirements.txt

# 复制模型文件到容器中
COPY ./models/* /root/.cache/huggingface/hub/

# 复制服务代码
COPY . .

# 启动服务
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]