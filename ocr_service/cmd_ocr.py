import logging
import traceback
from PIL import Image
from surya.ocr import run_ocr
from surya.model.detection.model import load_model as load_det_model, load_processor as load_det_processor
from surya.model.recognition.model import load_model as load_rec_model
from surya.model.recognition.processor import load_processor as load_rec_processor
from typing import List, Tuple, Any

logger = logging.getLogger(__name__)

def reformat_ocr_results(predictions: List[Any]) -> List[List[Tuple[List[List[float]], Tuple[str, float]]]]:
    """
    重新格式化OCR结果
    """
    try:
        logger.info(f"开始重新格式化OCR结果，预测数量: {len(predictions)}")
        reformatted_results = []
        
        for idx, pred in enumerate(predictions):
            logger.debug(f"处理第 {idx+1} 个预测结果")
            page_results = []
            
            if not hasattr(pred, 'text_lines'):
                logger.error(f"预测结果 {idx+1} 没有 text_lines 属性")
                logger.error(f"预测结果类型: {type(pred)}")
                logger.error(f"预测结果内容: {pred}")
                continue
                
            for line_idx, text_line in enumerate(pred.text_lines):
                try:
                    # 提取多边形坐标
                    polygon = text_line.polygon
                    # 提取文本和置信度
                    text_tuple = (text_line.text, float(text_line.confidence))
                    # 记录详细信息
                    logger.debug(f"文本行 {line_idx+1}: 文本='{text_line.text}', 置信度={text_line.confidence}")
                    logger.debug(f"多边形坐标: {polygon}")
                    
                    line_result = [polygon, text_tuple]
                    page_results.append(line_result)
                except Exception as line_error:
                    logger.error(f"处理文本行 {line_idx+1} 时出错: {str(line_error)}")
                    logger.error(traceback.format_exc())
                    continue
            
            reformatted_results.append([page_results])
            logger.info(f"第 {idx+1} 个预测结果包含 {len(page_results)} 个文本行")
        
        return reformatted_results
        
    except Exception as e:
        logger.error(f"格式化OCR结果时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise

def run_ocr_with_format(image_path: str) -> List[List[Tuple[List[List[float]], Tuple[str, float]]]]:
    """
    运行OCR并返回格式化的结果
    """
    try:
        logger.info(f"开始处理图片: {image_path}")
        
        # 加载图片
        try:
            image = Image.open(image_path)
            logger.info(f"成功加载图片，尺寸: {image.size}, 模式: {image.mode}")
        except Exception as img_error:
            logger.error(f"加载图片失败: {str(img_error)}")
            logger.error(traceback.format_exc())
            raise
        
        # 设置语言
        langs = ["zh", "en"]
        logger.info("使用语言设置: zh, en")
        
        # 加载模型
        try:
            logger.info("开始加载模型...")
            det_processor = load_det_processor()
            det_model = load_det_model()
            rec_model = load_rec_model()
            rec_processor = load_rec_processor()
            logger.info("模型加载完成")
        except Exception as model_error:
            logger.error(f"加载模型失败: {str(model_error)}")
            logger.error(traceback.format_exc())
            raise
        
        # 运行OCR
        try:
            logger.info("开始OCR处理...")
            predictions = run_ocr(
                [image], 
                [langs], 
                det_model, 
                det_processor, 
                rec_model, 
                rec_processor
            )
            logger.info(f"OCR处理完成，获得 {len(predictions)} 个预测结果")
        except Exception as ocr_error:
            logger.error(f"OCR处理失败: {str(ocr_error)}")
            logger.error(traceback.format_exc())
            raise
        
        # 重新格式化结果
        return reformat_ocr_results(predictions)
        
    except Exception as e:
        logger.error(f"OCR处理过程中发生错误: {str(e)}")
        logger.error("完整错误堆栈:")
        logger.error(traceback.format_exc())
        raise

def main():
    image_path = "/home/<USER>/py_project/surya-test/tablefiles/simpleteable.png"
    results = run_ocr_with_format(image_path)
    print(f"Reformatted results: {results[0]}")

if __name__ == "__main__":
    main()