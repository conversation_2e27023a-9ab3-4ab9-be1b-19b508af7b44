from fastapi import FastAPI, File, UploadFile, HTTPException
from PIL import Image
import io
import tempfile
import os
from pathlib import Path
from typing import Optional
# 导入OCR和表格处理模块
from cmd_ocr import run_ocr_with_format
from cmd_table import process_image_to_json
import logging
import traceback
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(title="OCR Service")

@app.post("/ocr")
async def perform_ocr(file: UploadFile = File(...)):
    """
    OCR识别接口
    """
    try:
        logger.info(f"开始处理文件: {file.filename}")
        logger.info(f"文件类型: {file.content_type}")
        
        # 读取上传的文件
        contents = await file.read()
        logger.info(f"文件大小: {len(contents)} bytes")
        
        # 保存为临时文件
        with tempfile.TemporaryDirectory() as temp_dir:
            image_path = os.path.join(temp_dir, file.filename)
            logger.info(f"临时文件路径: {image_path}")
            
            with open(image_path, "wb") as f:
                f.write(contents)
            
            # 验证图片
            try:
                with Image.open(image_path) as img:
                    logger.info(f"图片尺寸: {img.size}, 模式: {img.mode}")
            except Exception as img_error:
                logger.error(f"图片验证失败: {str(img_error)}")
                logger.error(traceback.format_exc())
                raise ValueError(f"Invalid image: {str(img_error)}")
            
            # 运行OCR识别
            logger.info("开始OCR识别")
            try:
                results = run_ocr_with_format(image_path)
                logger.info(f"OCR识别完成，结果数量: {len(results) if results else 0}")
                return {"result": results[0]}
            except Exception as ocr_error:
                logger.error(f"OCR处理失败: {str(ocr_error)}")
                logger.error("详细错误信息:")
                logger.error(traceback.format_exc())
                raise
            
    except ValueError as ve:
        logger.error(f"输入验证错误: {str(ve)}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"处理过程中发生错误: {str(e)}")
        logger.error("完整错误堆栈:")
        logger.error(traceback.format_exc())
        exc_type, exc_value, exc_traceback = sys.exc_info()
        logger.error("异常类型: %s", exc_type.__name__)
        logger.error("异常消息: %s", str(exc_value))
        logger.error("异常位置:")
        for frame in traceback.extract_tb(exc_traceback):
            logger.error("  文件 %s, 行 %d, 在 %s", frame.filename, frame.lineno, frame.name)
            logger.error("    %s", frame.line)
        
        raise HTTPException(
            status_code=500,
            detail={
                "error": str(e),
                "type": exc_type.__name__,
                "traceback": traceback.format_exc()
            }
        )

@app.post("/extract-table")
async def extract_table(file: UploadFile = File(...)):
    """
    表格识别接口
    
    Args:
        file: 上传的图片文件
        
    Returns:
        dict: 表格识别结果（JSON格式）
    """
    try:
        # 读取上传的文件
        contents = await file.read()
        
        # 保存为临时文件
        with tempfile.TemporaryDirectory() as temp_dir:
            image_path = os.path.join(temp_dir, file.filename)
            with open(image_path, "wb") as f:
                f.write(contents)
            
            # 处理表格
            json_data = process_image_to_json(image_path, save_json=False)
            
            if not json_data:
                raise HTTPException(status_code=500, detail="Failed to process table")
                
            return {"result": json_data}
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy"}