version: '3'

services:
  nginx:
    image: nginx:latest
    ports:
      - "8000:80"
      - "443:443"
    volumes:
      - ./backend:/app
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
      - adminer
    networks:
      - default

  db:
    image: postgres:12
    restart: always
    volumes:
      - app-db-data:/var/lib/postgresql/data/pgdata
    env_file:
      - .env
    environment:
      - PGDATA=/var/lib/postgresql/data/pgdata
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
      - POSTGRES_USER=${POSTGRES_USER?Variable not set}
      - POSTGRES_DB=${POSTGRES_DB?Variable not set}
    ports:
      - "5432:5432"

  adminer:
    image: adminer
    restart: always
    depends_on:
      - db
    environment:
      - ADMINER_DESIGN=pepa-linha-dark
    ports:
      - "8080:8080"

  redis:
    image: redis:latest
    ports:
      - "63790:6379"
  
  celery_worker:
    image: "${DOCKER_IMAGE_BACKEND?Variable not set}:${TAG-latest}"
    command: celery -A app.celery_init worker --loglevel=info
    volumes:
      - ./backend:/app
    depends_on:
      - db
      - redis
    env_file:
      - .env
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - POSTGRES_SERVER=db
      # 添加 CUDA 相关环境变量
      - NVIDIA_VISIBLE_DEVICES=all
      - TORCH_DEVICE=cuda
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  celery_beat:
    image: "${DOCKER_IMAGE_BACKEND?Variable not set}:${TAG-latest}"
    command: celery -A app.celery_init beat --loglevel=info
    depends_on:
      - db
      - redis
    env_file:
      - .env
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0

  ocr_service:
    build:
      context: ./ocr_service
      dockerfile: Dockerfile.ocr
    ports:
      - "8001:8000"
    volumes:
      - ./ocr_service/models:/root/.cache/huggingface/hub
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - TORCH_DEVICE=cuda
    networks:
      - default

  paddle_ocr:
    build:
      context: ./paddle-ocr
      dockerfile: Dockerfile.paddleocr
    image: paddleocr/paddleocr:latest
    ports:
      - "8002:8000"
    # volumes:
    #   - ./paddle-ocr/models:/root/.cache/huggingface/hub
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - TORCH_DEVICE=cuda
    networks:
      - default

  backend:
    image: "${DOCKER_IMAGE_BACKEND?Variable not set}:${TAG-latest}"
    restart: always
    depends_on:
      - db
      - redis
    env_file:
      - .env
    environment:
      - DOMAIN=${DOMAIN}
      - ENVIRONMENT=${ENVIRONMENT}
      - BACKEND_CORS_ORIGINS=${BACKEND_CORS_ORIGINS}
      - SECRET_KEY=${SECRET_KEY?Variable not set}
      - FIRST_SUPERUSER=${FIRST_SUPERUSER?Variable not set}
      - FIRST_SUPERUSER_PASSWORD=${FIRST_SUPERUSER_PASSWORD?Variable not set}
      - USERS_OPEN_REGISTRATION=${USERS_OPEN_REGISTRATION}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - EMAILS_FROM_EMAIL=${EMAILS_FROM_EMAIL}
      - POSTGRES_SERVER=db
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER?Variable not set}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
      - SENTRY_DSN=${SENTRY_DSN}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - OCR_SERVICE_URL=http://ocr_service:8000
      # CUDA 相关环境变量
      - NVIDIA_VISIBLE_DEVICES=all
      - TORCH_DEVICE=cuda
    volumes:
      - ./backend/:/app
    build:
      context: ./backend
      args:
        INSTALL_DEV: ${INSTALL_DEV-false}
    platform: linux/amd64
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    # command: sleep infinity  # Infinite loop to keep container alive doing nothing
    command: /start-reload.sh
    ports:
      - "8888:80"

volumes:
  app-db-data:

networks:
  default:
    driver: bridge
