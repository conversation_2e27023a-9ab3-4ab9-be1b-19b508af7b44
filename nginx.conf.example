events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:80;
    }

    upstream adminer {
        server adminer:8080;
    }

    server {
        listen 80;
        server_name **************;
        client_max_body_size 200M;

        location / {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /static/result/ {
            alias /app/yolov9/stitched_image/;
        }

        location /drawings_upload/ {
            alias /app/drawings_upload/;
        }

        location /adminer {
            proxy_pass http://adminer;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # 如果您确实需要在本地使用 HTTPS，可以取消注释以下部分
    # 并确保您有自签名的 SSL 证书
    # server {
    #     listen 443 ssl;
    #     server_name localhost;

    #     ssl_certificate /etc/nginx/ssl/localhost.crt;
    #     ssl_certificate_key /etc/nginx/ssl/localhost.key;

    #     location / {
    #         proxy_pass http://backend;
    #         proxy_set_header Host $host;
    #         proxy_set_header X-Real-IP $remote_addr;
    #     }

    #     location /adminer {
    #         proxy_pass http://adminer;
    #         proxy_set_header Host $host;
    #         proxy_set_header X-Real-IP $remote_addr;
    #     }
    # }
}
