# FastAPI 项目 - 后端

## 要求

* [Docker](https://www.docker.com/)
* [Poetry](https://python-poetry.org/) 用于Python包和环境管理

## 本地开发

### 启动项目

使用Docker Compose启动项目:

```bash
docker compose up -d
```

现在您可以在浏览器中访问以下URL:

- 前端(使用Docker构建,基于路径处理路由): http://localhost
- 后端(基于OpenAPI的JSON Web API): http://localhost/api/
- 自动交互式文档 (Swagger UI): http://localhost/docs
- Adminer (数据库Web管理工具): http://localhost:8080
- Traefik UI (查看代理如何处理路由): http://localhost:8090

**注意**: 首次启动堆栈时,可能需要一分钟才能准备就绪。后端需要等待数据库准备好并配置所有内容。您可以通过查看日志来监控进度。

查看日志:

```bash
docker compose logs
```

查看特定服务的日志:

```bash
docker compose logs backend
```

### 依赖管理

默认使用[Poetry](https://python-poetry.org/)管理依赖。在`./backend/`目录下,您可以通过以下命令安装所有依赖:

```bash
poetry install
```

启动新环境的shell会话:

```bash
poetry shell
```

确保您的编辑器使用正确的Python虚拟环境。

### 代码修改

- 修改或添加SQLModel模型: `./backend/app/models.py`
- API端点: `./backend/app/api/`
- CRUD(创建、读取、更新、删除)工具: `./backend/app/crud.py`

### 启用开放用户注册

默认情况下,后端禁用了用户注册,但已经有一个注册用户的路由。如果您想允许用户自行注册,可以在`.env`文件中将环境变量`USERS_OPEN_REGISTRATION`设置为`True`。

修改环境变量后,重启Docker容器以应用更改:

```bash
docker compose up -d
```

### 数据库迁移

使用Alembic进行数据库迁移。在后端容器中执行以下操作:

1. 创建迁移版本:

```bash
alembic revision --autogenerate -m "迁移说明"
```

2. 应用迁移:

```bash
alembic upgrade head
```

### 测试

运行后端测试:

```bash
bash ./scripts/test.sh
```

测试使用Pytest运行,修改和添加测试可在`./backend/app/tests/`目录中进行。

如果使用GitHub Actions,测试将自动运行。

### 测试覆盖率

运行测试后,会生成一个`htmlcov/index.html`文件,您可以在浏览器中打开它查看测试覆盖率。

## 其他说明

更多详细信息请参考原始README文件,包括VS Code配置、Docker Compose覆盖、迁移等高级主题。