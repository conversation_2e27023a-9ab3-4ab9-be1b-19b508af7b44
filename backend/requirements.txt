alembic==1.13.1 ; python_version >= "3.10" and python_version < "4.0"
amqp==5.2.0 ; python_version >= "3.10" and python_version < "4.0"
annotated-types==0.6.0 ; python_version >= "3.10" and python_version < "4.0"
anyio==4.3.0 ; python_version >= "3.10" and python_version < "4.0"
async-timeout==4.0.3 ; python_version >= "3.10" and python_full_version < "3.11.3"
bcrypt==4.0.1 ; python_version >= "3.10" and python_version < "4.0"
billiard==4.2.0 ; python_version >= "3.10" and python_version < "4.0"
cachetools==5.3.3 ; python_version >= "3.10" and python_version < "4.0"
celery==5.4.0 ; python_version >= "3.10" and python_version < "4.0"
certifi==2024.2.2 ; python_version >= "3.10" and python_version < "4.0"
chardet==5.2.0 ; python_version >= "3.10" and python_version < "4.0"
charset-normalizer==3.3.2 ; python_version >= "3.10" and python_version < "4.0"
click-didyoumean==0.3.1 ; python_version >= "3.10" and python_version < "4.0"
click-plugins==1.1.1 ; python_version >= "3.10" and python_version < "4.0"
click-repl==0.3.0 ; python_version >= "3.10" and python_version < "4.0"
click==8.1.7 ; python_version >= "3.10" and python_version < "4.0"
colorama==0.4.6 ; python_version >= "3.10" and python_version < "4.0" and (sys_platform == "win32" or platform_system == "Windows")
cssselect==1.2.0 ; python_version >= "3.10" and python_version < "4.0"
cssutils==2.9.0 ; python_version >= "3.10" and python_version < "4.0"
dnspython==2.6.1 ; python_version >= "3.10" and python_version < "4.0"
email-validator==2.1.1 ; python_version >= "3.10" and python_version < "4.0"
emails==0.6 ; python_version >= "3.10" and python_version < "4.0"
exceptiongroup==1.2.0 ; python_version >= "3.10" and python_version < "3.11"
fastapi==0.109.2 ; python_version >= "3.10" and python_version < "4.0"
greenlet==3.0.3 ; python_version >= "3.10" and python_version < "4.0" and (platform_machine == "aarch64" or platform_machine == "ppc64le" or platform_machine == "x86_64" or platform_machine == "amd64" or platform_machine == "AMD64" or platform_machine == "win32" or platform_machine == "WIN32")
gunicorn==22.0.0 ; python_version >= "3.10" and python_version < "4.0"
h11==0.14.0 ; python_version >= "3.10" and python_version < "4.0"
httpcore==1.0.4 ; python_version >= "3.10" and python_version < "4.0"
httptools==0.6.1 ; python_version >= "3.10" and python_version < "4.0"
httpx==0.25.2 ; python_version >= "3.10" and python_version < "4.0"
idna==3.7 ; python_version >= "3.10" and python_version < "4.0"
jinja2==3.1.4 ; python_version >= "3.10" and python_version < "4.0"
kombu==5.3.7 ; python_version >= "3.10" and python_version < "4.0"
lxml==5.1.0 ; python_version >= "3.10" and python_version < "4.0"
mako==1.3.2 ; python_version >= "3.10" and python_version < "4.0"
markupsafe==2.1.5 ; python_version >= "3.10" and python_version < "4.0"
packaging==24.0 ; python_version >= "3.10" and python_version < "4.0"
passlib[bcrypt]==1.7.4 ; python_version >= "3.10" and python_version < "4.0"
premailer==3.10.0 ; python_version >= "3.10" and python_version < "4.0"
prompt-toolkit==3.0.47 ; python_version >= "3.10" and python_version < "4.0"
psycopg-binary==3.1.18 ; implementation_name != "pypy" and python_version >= "3.10" and python_version < "4.0"
psycopg[binary]==3.1.18 ; python_version >= "3.10" and python_version < "4.0"
pydantic-core==2.16.3 ; python_version >= "3.10" and python_version < "4.0"
pydantic-settings==2.2.1 ; python_version >= "3.10" and python_version < "4.0"
pydantic==2.6.4 ; python_version >= "3.10" and python_version < "4.0"
pyjwt==2.8.0 ; python_version >= "3.10" and python_version < "4.0"
python-dateutil==2.9.0.post0 ; python_version >= "3.10" and python_version < "4.0"
python-dotenv==1.0.1 ; python_version >= "3.10" and python_version < "4.0"
python-multipart==0.0.7 ; python_version >= "3.10" and python_version < "4.0"
pytz==2024.1 ; python_version >= "3.10" and python_version < "4.0"
pyyaml==6.0.1 ; python_version >= "3.10" and python_version < "4.0"
redis==5.0.6 ; python_version >= "3.10" and python_version < "4.0"
requests==2.32.0 ; python_version >= "3.10" and python_version < "4.0"
sentry-sdk[fastapi]==1.41.0 ; python_version >= "3.10" and python_version < "4.0"
six==1.16.0 ; python_version >= "3.10" and python_version < "4.0"
sniffio==1.3.1 ; python_version >= "3.10" and python_version < "4.0"
sqlalchemy==2.0.28 ; python_version >= "3.10" and python_version < "4.0"
sqlmodel==0.0.16 ; python_version >= "3.10" and python_version < "4.0"
starlette==0.36.3 ; python_version >= "3.10" and python_version < "4.0"
tenacity==8.2.3 ; python_version >= "3.10" and python_version < "4.0"
typing-extensions==4.10.0 ; python_version >= "3.10" and python_version < "4.0"
tzdata==2024.1 ; python_version >= "3.10" and python_version < "4.0"
urllib3==2.2.1 ; python_version >= "3.10" and python_version < "4.0"
uvicorn[standard]==0.24.0.post1 ; python_version >= "3.10" and python_version < "4.0"
uvloop==0.19.0 ; (sys_platform != "win32" and sys_platform != "cygwin") and platform_python_implementation != "PyPy" and python_version >= "3.10" and python_version < "4.0"
vine==5.1.0 ; python_version >= "3.10" and python_version < "4.0"
watchfiles==0.21.0 ; python_version >= "3.10" and python_version < "4.0"
wcwidth==0.2.13 ; python_version >= "3.10" and python_version < "4.0"
websockets==12.0 ; python_version >= "3.10" and python_version < "4.0"
# yolov9 requirements
# Usage: pip install -r requirements.txt

# Base ------------------------------------------------------------------------
gitpython
ipython
matplotlib>=3.2.2
numpy>=1.18.5
opencv-python>=4.1.1
Pillow==9.0.1
psutil
# PyYAML>=5.3.1
# requests>=2.23.0
scipy>=1.4.1
thop>=0.1.1
torch>=1.7.0
torchvision>=0.8.1
tqdm>=4.64.0
# protobuf<=3.20.1

# Logging ---------------------------------------------------------------------
tensorboard>=2.4.1
# clearml>=1.2.0
# comet

# Plotting --------------------------------------------------------------------
pandas>=1.1.4
seaborn>=0.11.0

# Export ----------------------------------------------------------------------
# coremltools>=6.0
# onnx>=1.9.0
# onnx-simplifier>=0.4.1
# nvidia-pyindex
# nvidia-tensorrt
# scikit-learn<=1.1.2
# tensorflow>=2.4.1
# tensorflowjs>=3.9.0
# openvino-dev

# Deploy ----------------------------------------------------------------------
# tritonclient[all]~=2.24.0

# Extras ----------------------------------------------------------------------
# mss
albumentations>=1.0.3
pycocotools>=2.0
pdf2image==1.17.0
openpyxl==3.1.5
shapely==2.0.6

# fastapicdn
fastapi-offline