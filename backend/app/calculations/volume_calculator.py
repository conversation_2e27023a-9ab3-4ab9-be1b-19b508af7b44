from typing import Dict, List, Any, Optional
import logging
from enum import Enum
import json

from app.models.drawing import Drawing

logger = logging.getLogger(__name__)

class DrawingType(Enum):
    """图纸类型枚举"""
    # 建筑工程
    LIANG = ("梁", "建筑工程")
    BAN = ("板", "建筑工程")
    ZHU = ("柱", "建筑工程")
    QIANG = ("墙", "建筑工程")
    JC_BUZHI = ("基础布置图", "建筑工程")
    JC_XIANGTU = ("基础详图", "建筑工程")
    OTHER_JIANZHUGONGCHENG = ("其他建筑", "建筑工程")

    # 设备安装
    MENCHUANG = ("门窗", "设备安装")
    OTHER_SHEBEIANZHUANG = ("其他设备", "设备安装")

    def __init__(self, category: str, discipline: str):
        self.category = category
        self.discipline = discipline

# 定义需要使用比例尺的图纸类型
TYPES_NEED_GRID = {DrawingType.JC_XIANGTU}

def calculate_volume_liang(table_data: List[dict]) -> Dict[str, Any]:
    """
    计算梁的体积
    """
    pass

def calculate_volume_ban(table_data: List[dict]) -> Dict[str, Any]:
    """
    计算板的体积
    """
    pass

def calculate_volume_zhu(table_data: List[dict]) -> Dict[str, Any]:
    """
    计算柱的体积
    """
    pass

def calculate_volume_qiang(table_data: List[dict]) -> Dict[str, Any]:
    """
    计算墙的体积
    """
    pass    

def calculate_volume_menchuang(table_data: List[dict]) -> Dict[str, Any]:
    """
    计算门窗的体积
    """
    pass

def calculate_volume_other_jianzhugongcheng(table_data: List[dict]) -> Dict[str, Any]:
    """
    计算其他建筑构件的体积
    """
    pass

def calculate_volume_other_shebeianzhuang(table_data: List[dict]) -> Dict[str, Any]:
    """
    计算其他设备构件的体积
    """
    pass

def calculate_volume_jc_xiangtu(table_data: List[dict], grid_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    计算基础详图的体积
    
    Args:
        table_data: 表格数据
        grid_data: 标尺数据，格式如 {"coordinates": ["440,512,702,512"], "length": "3600"}
    
    Returns:
        Dict包含计算结果和新的表格数据
    """
    if not grid_data:
        raise ValueError("基础详图计算需要标尺数据")
    try:
        # 解析标尺数据获取比例
        coords = [int(x) for x in grid_data["coordinates"][0].split(",")]
        pixel_length = ((coords[2] - coords[0]) ** 2 + (coords[3] - coords[1]) ** 2) ** 0.5
        real_length = float(grid_data["length"])
        scale = real_length / pixel_length  # mm/pixel
        
        volume_results = {}
        volume_details = []
        total_volume = 0
        
        for row in table_data[0]["data"]:
            mark = row[0]  # 基础标记
            
            # 解析平面和剖面数据
            ping_data = json.loads(row[1])
            pou_data = json.loads(row[2])
            
            # 获取平面框和剖面框
            ping_boxes = ping_data["yolo_ping"]
            pou_boxes = pou_data["yolo_pou"]
            
            # 按x坐标排序平面框
            ping_boxes.sort(key=lambda x: x[0])
            # 按y坐标排序剖面框
            pou_boxes.sort(key=lambda x: x[1])
            
            # 计算每个框的体积
            volumes = []
            for i, ping_box in enumerate(ping_boxes):
                if i >= len(pou_boxes):
                    break
                    
                # 计算平面面积
                ping_width = (ping_box[2] - ping_box[0]) * scale  # mm
                ping_height = (ping_box[3] - ping_box[1]) * scale  # mm
                area = ping_width * ping_height  # mm²
                
                # 计算剖面高度
                pou_box = pou_boxes[i]
                height = (pou_box[3] - pou_box[1]) * scale  # mm
                
                # 计算体积 (转换为m³)
                volume = (area * height) / 1e9  # mm³ -> m³
                volumes.append(volume)
                total_volume += volume
            
            # 格式化体积字符串
            volume_str = " + ".join([f"{v:.2f}" for v in volumes])
            volume_str += f" = {sum(volumes):.2f}"
            
            volume_results[mark] = {
                "volumes": volumes,
                "total": sum(volumes),
                "detail": volume_str
            }
            
            # 添加到明细列表
            volume_details.append([
                mark,
                volume_str,
                "m³"
            ])
        
        # 创建体积计算结果表
        volume_table = {
            "title": "基础详图体积计算结果",
            "columns": ["基础标记", "体积计算(m³)", "单位"],
            "data": volume_details
        }
        
        # 创建汇总表
        summary_table = {
            "title": "基础详图体积汇总",
            "columns": ["总体积(m³)"],
            "data": [[f"{total_volume:.2f}"]]
        }
        
        return {
            "volume_results": volume_results,
            "new_tables": [volume_table, summary_table]
        }
        
    except Exception as e:
        logger.error(f"计算基础详图体积时出错: {str(e)}")
        raise ValueError(f"计算体积失败: {str(e)}")

def calculate_volume_jc_buzhi(table_data: List[dict], grid_data: Optional[Dict[str, Any]] = None, session=None, drawing_set_id=None) -> Dict[str, Any]:
    """
    计算基础布置图的体积
    
    Args:
        table_data: 布置图的表格数据
        grid_data: 不需要
        session: 数据库会话
        drawing_set_id: 图纸集ID
    """
    if not session or not drawing_set_id:
        raise ValueError("基础布置图计算需要数据库会话和图纸集ID")

    try:
        # 1. 获取同一图纸集中的基础详图
        detail_drawing = session.query(Drawing).filter(
            Drawing.drawing_set_id == drawing_set_id,
            Drawing.category == "基础详图",
            Drawing.discipline == "建筑工程"
        ).first()

        if not detail_drawing or not detail_drawing.finally_result:
            raise ValueError("未找到对应的基础详图或详图未完成计算")

        # 2. 获取详图的体积计算结果
        detail_tables = detail_drawing.finally_result.get("table", [])
        volume_table = next((table for table in detail_tables 
                           if table.get("title") == "基础详图体积计算结果"), None)
        
        if not volume_table:
            raise ValueError("基础详图中未找到体积计算结果")

        # 创建基础标记到体积的映射
        mark_to_volume = {}
        for row in volume_table["data"]:
            mark = row[0]
            # 提取等号后面的总体积
            volume = float(row[1].split("=")[1].strip().split()[0])
            mark_to_volume[mark] = volume

        # 3. 计算布置图中每个构件的总体积
        total_volume = 0
        volume_details = []
        
        for row in table_data[0]["data"]:
            mark = row[0]  # 基础符号
            count = int(row[2].replace("个", ""))  # 数量
            
            if mark in mark_to_volume:
                single_volume = mark_to_volume[mark]
                total_mark_volume = single_volume * count
                total_volume += total_mark_volume
                
                # 格式化体积计算字符串
                volume_str = f"{single_volume:.2f} × {count} = {total_mark_volume:.2f}"
                volume_details.append([
                    mark,
                    str(count),
                    f"{single_volume:.2f}",
                    volume_str,
                    "m³"
                ])
            else:
                logger.warning(f"未找到基础标记 {mark} 的体积数据")

        # 4. 创建结果表格
        volume_table = {
            "title": "基础布置图体积计算结果",
            "columns": ["基础标记", "数量", "单个体积(m³)", "体积计算(m³)", "单位"],
            "data": volume_details
        }

        # 创建汇总表
        summary_table = {
            "title": "基础布置图体积汇总",
            "columns": ["总体积(m³)"],
            "data": [[f"{total_volume:.2f}"]]
        }

        return {
            "volume_results": {
                "total": total_volume,
                "details": {row[0]: row[3] for row in volume_details}
            },
            "new_tables": [volume_table, summary_table]
        }

    except Exception as e:
        logger.error(f"计算基础布置图体积时出错: {str(e)}")
        raise ValueError(f"计算体积失败: {str(e)}")


# 计算函数映射表
VOLUME_CALCULATORS = {
    DrawingType.LIANG: calculate_volume_liang,
    DrawingType.BAN: calculate_volume_ban,
    DrawingType.ZHU: calculate_volume_zhu,
    DrawingType.QIANG: calculate_volume_qiang,
    DrawingType.JC_BUZHI: calculate_volume_jc_buzhi,
    DrawingType.JC_XIANGTU: calculate_volume_jc_xiangtu,
    DrawingType.MENCHUANG: calculate_volume_menchuang,
    # 其他类型可以添加对应的计算函数
}


def calculate_volume(category: str, discipline: str, table_data: List[dict], grid_data: Optional[Dict[str, Any]] = None, session=None, drawing_set_id=None) -> Dict[str, Any]:
    """
    统一的体积计算入口
    """
    try:
        # 修改这里：直接遍历 DrawingType 来查找匹配的类型
        drawing_type = None
        for dt in DrawingType:
            if dt.category == category and dt.discipline == discipline:
                drawing_type = dt
                break
                
        if drawing_type is None:
            raise ValueError(f"Unsupported drawing type: {category}-{discipline}")

        calculator = VOLUME_CALCULATORS.get(drawing_type)
        if not calculator:
            raise ValueError(f"No calculator found for drawing type: {category}-{discipline}")

        if drawing_type in TYPES_NEED_GRID and not grid_data:
            raise ValueError(f"Drawing type {category}-{discipline} requires grid data for calculation")

        # 不同函数签名传递参数
        if drawing_type == DrawingType.JC_XIANGTU:
            return calculator(table_data, grid_data)
        elif drawing_type == DrawingType.JC_BUZHI:
            return calculator(table_data, grid_data, session, drawing_set_id)
        else:
            return calculator(table_data, grid_data)

    except Exception as e:
        logger.error(f"Volume calculation error: {str(e)}")
        raise ValueError(f"计算体积失败: {str(e)}")

