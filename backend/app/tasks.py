from app.celery_init import celery_app
from time import sleep


@celery_app.task
def convert_image(image_path, output_format):
    return f'Converted {image_path} to {output_format}'

@celery_app.task
def yolo_object_detection(image_path):
    # 实现YOLO目标检测逻辑
    pass

@celery_app.task
def preprocess_image(image_path):
    # 实现图像预处理逻辑
    pass

@celery_app.task
def task_flow_1(image_path):
    return yolo_object_detection.delay(image_path)

@celery_app.task
def task_flow_2(image_path):
    preprocessed_image = preprocess_image.delay(image_path)
    return yolo_object_detection.delay(preprocessed_image.get())

