from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import select, func

from datetime import datetime as da
from pytz import timezone

from app.models import (
    Project,
    ProjectCreate,
    ProjectUpdate,
    ProjectPublic,
    ProjectsPublic,
    Message,
)
from app.api.deps import CurrentUser, SessionDep
from app.api.response_schemas import (
    SuccessResponse,
    ErrorResponse,
    ProjectNotFoundError,
    InsufficientPermissionsError,
)

router = APIRouter()


@router.get("/")
def read_projects(
    session: SessionDep,
    current_user: CurrentUser,
    skip: int = 0,
    limit: int = 100,
) -> SuccessResponse:
    """
    Retrieve projects.
    """
    if current_user.is_superuser:
        count_statement = select(func.count()).select_from(Project)
        count = session.exec(count_statement).one()
        statement = select(Project).offset(skip).limit(limit)
        projects = session.exec(statement).all()
    else:
        count_statement = (
            select(func.count())
            .select_from(Project)
            .where(Project.owner_id == current_user.id)
        )
        count = session.exec(count_statement).one()
        statement = (
            select(Project)
            .where(Project.owner_id == current_user.id)
            .offset(skip)
            .limit(limit)
        )
        projects = session.exec(statement).all()

    return SuccessResponse(data=ProjectsPublic(data=projects, count=count))


@router.get("/{id}")
def read_project(
    session: SessionDep,
    current_user: CurrentUser,
    id: int = None,
) -> SuccessResponse:
    """
    Get project by ID.
    """
    project = session.get(Project, id)
    if not project:
        raise HTTPException(status_code=404, detail=ProjectNotFoundError().dict())
    if not current_user.is_superuser and (project.owner_id != current_user.id):
        raise HTTPException(
            status_code=403, detail=InsufficientPermissionsError().dict()
        )
    return SuccessResponse(data=project)


@router.post("/")
def create_project(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    project_in: ProjectCreate,
) -> SuccessResponse:
    """
    Create new project.
    """
    project = Project.model_validate(
        project_in,
        update={
            "owner_id": current_user.id,
            "created_at": da.now(timezone("Asia/Shanghai")),
        },
    )
    session.add(project)
    session.commit()
    session.refresh(project)
    return SuccessResponse(data=project)


@router.delete("/{project_id}")
def delete_project(session: SessionDep, current_user: CurrentUser, project_id: int) -> Message:
    """
    Delete an project.
    """
    project = session.get(Project, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="project not found")
    if not current_user.is_superuser and (project.owner_id != current_user.id):
        raise HTTPException(status_code=400, detail="Not enough permissions")
    session.delete(project)
    session.commit()
    return SuccessResponse(message="Project deleted successfully")

# @router.put("/{id}")
# def update_project(
#     *,
#     session: SessionDep,
#     current_user: CurrentUser,
#     id: int,
#     project_in: ProjectUpdate,
# ) -> SuccessResponse:
#     """
#     Update a project.
#     """
#     project = session.get(Project, id)
#     if not project:
#         raise HTTPException(status_code=404, detail=ProjectNotFoundError().dict())
#     if not current_user.is_superuser and (project.owner_id != current_user.id):
#         raise HTTPException(
#             status_code=403, detail=InsufficientPermissionsError().dict()
#         )
#     update_dict = project_in.model_dump(exclude_unset=True)
#     project.sqlmodel_update(update_dict)
#     session.add(project)
#     session.commit()
#     session.refresh(project)
#     return SuccessResponse(data=project)


# @router.delete("/{id}")
# def delete_project(
#     session: SessionDep,
#     current_user: CurrentUser,
#     id: int = None,
# ) -> SuccessResponse:
#     """
#     Delete a project.
#     """
#     project = session.get(Project, id)
#     if not project:
#         raise HTTPException(status_code=404, detail=ProjectNotFoundError().dict())
#     if not current_user.is_superuser and (project.owner_id != current_user.id):
#         raise HTTPException(
#             status_code=403, detail=InsufficientPermissionsError().dict()
#         )
#     session.delete(project)
#     session.commit()
#     return SuccessResponse(data=Message(message="Project deleted successfully"))
