from typing import Any, Optional
from fastapi import APIRouter, HTTPException
from sqlmodel import select, func
from datetime import datetime as da
from pytz import timezone

from app.api.deps import CurrentUser, SessionDep
from app.api.response_schemas import ResponseBase
from app.models import Drawing, DrawingSet, DrawingSetResponse, DrawingPublic
from app.api.request_schemas import (
    DrawingSetCreate,
    DrawingSetUpdateDrawings,
)

import logging

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/", response_model=ResponseBase)
async def create_drawing_set(
    session: SessionDep,
    current_user: CurrentUser,
    data: DrawingSetCreate,
) -> Any:
    """
    创建套图
    
    Args:
        data: 包含套图名称、描述和图纸ID列表的请求数据
    """
    try:
        # 验证所有图纸是否存在且属于同一个项目
        drawings = session.exec(
            select(Drawing)
            .where(Drawing.id.in_(data.drawing_ids))
        ).all()
        
        if len(drawings) != len(data.drawing_ids):
            raise HTTPException(status_code=400, detail="Some drawings not found")
            
        project_ids = {d.project_id for d in drawings}
        if len(project_ids) > 1:
            raise HTTPException(
                status_code=400, 
                detail="Drawings must belong to the same project"
            )

        # 创建套图
        drawing_set = DrawingSet(
            name=data.name,
            project_id=list(project_ids)[0],
            created_by=current_user.id,
            description=data.description,
            created_at=da.now(timezone("Asia/Shanghai")),
        )
        session.add(drawing_set)
        session.flush()

        # 更新图纸关联 - 修改这部分
        for drawing in drawings:
            drawing.drawing_set_id = drawing_set.id
            # 如果图纸已经有类型，使用现有类型
            if drawing.drawing_type:
                drawing_set.required_types[drawing.drawing_type.value] = True
            
        session.commit()
        session.refresh(drawing_set)

        # 构建响应数据
        response_data = DrawingSetResponse(
            id=drawing_set.id,
            name=drawing_set.name,
            project_id=drawing_set.project_id,
            description=drawing_set.description,
            created_at=drawing_set.created_at,
            created_by=drawing_set.created_by,
            required_types=drawing_set.required_types,
            drawings=[DrawingPublic.from_orm(d) for d in drawings]
        )

        return ResponseBase(
            code=200,
            message="Drawing set created successfully",
            data=response_data.dict()
        )

    except Exception as e:
        logger.error(f"Error creating drawing set: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/", response_model=ResponseBase)
async def list_drawing_sets(
    session: SessionDep,
    current_user: CurrentUser,
    project_id: Optional[int] = None,
    skip: int = 0,
    limit: int = 10
) -> Any:
    """
    获取套图列表
    
    Args:
        project_id: 可选的项目ID过滤
        skip: 分页起始位置
        limit: 每页数量
    """
    try:
        # 构建基础查询
        if current_user.is_superuser:
            query = select(DrawingSet)
            if project_id:
                query = query.where(DrawingSet.project_id == project_id)
        else:
            query = select(DrawingSet).where(DrawingSet.project_id == project_id)
            if not project_id:
                raise HTTPException(status_code=400, detail="Project ID is required")
        
        # 获取总数
        total = session.exec(select(func.count()).select_from(query.subquery())).one()
        
        # 获取分页数据
        drawing_sets = session.exec(
            query.offset(skip).limit(limit).order_by(DrawingSet.created_at.desc())
        ).all()

        # 获取每个套图关联的图纸
        result_items = []
        for ds in drawing_sets:
            drawings = session.exec(
                select(Drawing)
                .where(Drawing.drawing_set_id == ds.id)
                .order_by(Drawing.drawing_type)
            ).all()
            
            result_items.append({
                "id": ds.id,
                "name": ds.name,
                "project_id": ds.project_id,
                "created_at": ds.created_at,
                "created_by": ds.created_by,
                "description": ds.description,
                "required_types": ds.required_types,
                "drawings_count": len(drawings),
                "completion_status": sum(1 for t in ds.required_types.values() if t),
                "drawings": [
                    {
                        "id": d.id,
                        "file_name": d.file_name,
                        "file_url": d.file_url,
                        "category": d.category,
                        "drawing_type": d.drawing_type,
                        "status": d.status,
                        "uploaded_at": d.uploaded_at,
                        "project_id": d.project_id
                    }
                    for d in drawings
                ]
            })
        
        return ResponseBase(
            code=200,
            message="Success",
            data={
                "items": result_items,
                "total": total,
                "skip": skip,
                "limit": limit
            }
        )

    except Exception as e:
        logger.error(f"Error listing drawing sets: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/{set_id}", response_model=ResponseBase)
async def get_drawing_set(
    set_id: int,
    session: SessionDep,
    current_user: CurrentUser,
) -> Any:
    """
    获取套图详情
    
    Args:
        set_id: 套图ID
    """
    try:
        drawing_set = session.get(DrawingSet, set_id)
        if not drawing_set:
            raise HTTPException(status_code=404, detail="Drawing set not found")

        # 获取关联的图纸
        drawings = session.exec(
            select(Drawing)
            .where(Drawing.drawing_set_id == set_id)
            .order_by(Drawing.drawing_type)
        ).all()

        return ResponseBase(
            code=200,
            message="Success",
            data={
                "id": drawing_set.id,
                "name": drawing_set.name,
                "project_id": drawing_set.project_id,
                "description": drawing_set.description,
                "created_at": drawing_set.created_at,
                "created_by": drawing_set.created_by,
                "required_types": drawing_set.required_types,
                "drawings": [
                    {
                        "id": d.id,
                        "file_name": d.file_name,
                        "file_url": d.file_url,
                        "category": d.category,
                        "drawing_type": d.drawing_type,
                        "status": d.status,
                        "uploaded_at": d.uploaded_at
                    }
                    for d in drawings
                ]
            }
        )

    except Exception as e:
        logger.error(f"Error getting drawing set: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/{set_id}", response_model=ResponseBase)
async def update_drawing_set(
    set_id: int,
    data: DrawingSetUpdateDrawings,
    session: SessionDep,
    current_user: CurrentUser,
) -> Any:
    """
    更新套图内容
    
    Args:
        set_id: 套图ID
        data: 更新数据，包含要添加和移除的图纸信息
    """
    try:
        drawing_set = session.get(DrawingSet, set_id)
        if not drawing_set:
            raise HTTPException(status_code=404, detail="Drawing set not found")

        # 添加新图纸
        if data.add_drawings:
            for drawing_id, drawing_type in data.add_drawings:
                drawing = session.get(Drawing, drawing_id)
                if not drawing:
                    raise HTTPException(
                        status_code=400, 
                        detail=f"Drawing {drawing_id} not found"
                    )
                if drawing.project_id != drawing_set.project_id:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Drawing {drawing_id} belongs to different project"
                    )
                drawing.drawing_set_id = set_id
                drawing.drawing_type = drawing_type
                drawing_set.required_types[drawing_type.value] = True

        # 移除图纸
        if data.remove_drawing_ids:
            drawings_to_remove = session.exec(
                select(Drawing)
                .where(
                    Drawing.id.in_(data.remove_drawing_ids),
                    Drawing.drawing_set_id == set_id
                )
            ).all()
            
            for drawing in drawings_to_remove:
                drawing_set.required_types[drawing.drawing_type.value] = False
                drawing.drawing_set_id = None
                drawing.drawing_type = None

        # 更新时间戳
        drawing_set.updated_at = da.now(timezone("Asia/Shanghai"))
        
        session.commit()
        session.refresh(drawing_set)

        return ResponseBase(
            code=200,
            message="Drawing set updated successfully",
            data={
                "id": drawing_set.id,
                "name": drawing_set.name,
                "required_types": drawing_set.required_types,
                "drawings_count": len(drawing_set.drawings)
            }
        )

    except Exception as e:
        logger.error(f"Error updating drawing set: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/{set_id}", response_model=ResponseBase)
async def delete_drawing_set(
    set_id: int,
    session: SessionDep,
    current_user: CurrentUser,
) -> Any:
    """
    删除套图
    
    Args:
        set_id: 套图ID
    """
    try:
        drawing_set = session.get(DrawingSet, set_id)
        if not drawing_set:
            raise HTTPException(status_code=404, detail="Drawing set not found")

        # 解除所有关联图纸的关系
        drawings = session.exec(
            select(Drawing)
            .where(Drawing.drawing_set_id == set_id)
        ).all()
        
        for drawing in drawings:
            drawing.drawing_set_id = None
            drawing.drawing_type = None

        # 删除套图
        session.delete(drawing_set)
        session.commit()

        return ResponseBase(
            code=200,
            message="Drawing set deleted successfully",
            data={"id": set_id}
        )

    except Exception as e:
        logger.error(f"Error deleting drawing set: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))