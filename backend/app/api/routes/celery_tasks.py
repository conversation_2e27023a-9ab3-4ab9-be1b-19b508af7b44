from app.celery_init import celery_app
from celery.result import AsyncResult
from app.tasks import task_flow_1, task_flow_2, convert_image, recognize_drawing_components
from fastapi import APIRouter, Depends, HTTPException
import shutil

router = APIRouter()

@router.post("/detect")
async def detect_objects(image_path: str, output_path: str):
    task = convert_image.delay(image_path, output_path)
    return {"task_id": task.id}

@router.get("/task/{task_id}")
async def get_task_status(task_id: str):
    result = AsyncResult(task_id)
    return {
        "task_id": task_id,
        "status": result.state,
        "result": result.result if result.ready() else None
    }
    
# @router.post("/yolov9/")
# async def detect_image(file: UploadFile = File(...)):
#     # 保存上传的图片
#     file_location = f"/tmp/{file.filename}"
#     with open(file_location, "wb+") as file_object:
#         shutil.copyfileobj(file.file, file_object)
    
#     # 异步调用YOLOv9任务
#     task = recognize_drawing_components.delay(file_location)
    
#     return {"task_id": task.id}
