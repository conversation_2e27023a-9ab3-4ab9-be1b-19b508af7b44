import os
import uuid
import shutil
from datetime import datetime
from tempfile import SpooledTemporaryFile
import logging

from fastapi import UploadFile

logger = logging.getLogger(__name__)

def remove_file(path: str):
    """
    删除文件
    """
    try:
        os.unlink(path)
        logger.info(f"Temporary file {path} deleted successfully.")
    except Exception as e:
        logger.error(f"Error deleting file {path}: {e}")


def generate_unique_filename(original_filename: str) -> str:
    """
    生成唯一文件名
    """
    _, extension = os.path.splitext(original_filename)
    unique_id = str(uuid.uuid4())
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    unique_filename = f"{unique_id}_{timestamp}{extension}"
    return unique_filename


def save_file(file_path: str, file: UploadFile):
    """
    保存文件到服务器的文件系统中
    """
    directory = os.path.dirname(file_path)
    os.makedirs(directory, exist_ok=True)
    try:
        with open(file_path, "wb") as buffer:
            # 如果上传的文件大小超过一定值,file会是SpooledTemporaryFile类型,否则是普通的BytesIO
            if isinstance(file, SpooledTemporaryFile):
                file.seek(0)
                shutil.copyfileobj(file, buffer)
            else:
                shutil.copyfileobj(file.file, buffer)
    except Exception as e:
        print(f"Error saving file: {str(e)}")
        raise
