import os
from typing import Any, List
from pytz import timezone
from datetime import datetime as da
import logging
import re
import json

from PIL import Image
from fastapi import APIRouter, HTTPException, File, Form, UploadFile, BackgroundTasks
from fastapi.responses import FileResponse
from sqlmodel import func, select
from sqlmodel import select
from sqlalchemy.exc import SQLAlchemyError
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
import tempfile
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import Optional, List

from pdf2image import convert_from_path
from app.api.deps import CurrentUser, SessionDep
from app.core.db import celery_engine
from app.api.response_schemas import ResponseBase
from app.core.config import settings
from app.api.request_schemas import (
    RecognizeRequest, 
    GetDrawingResultRequest, 
    UpdateDrawingResultRequest, 
    GridUpdateRequest,
    DrawingSetCreate,
    DrawingSetUpdateDrawings
)
from app.models import Drawing, DrawingPublic, Project, RecognitionTask, DrawingSet, DrawingSetResponse
from app.tasks import recognize_drawing_components
from app.utils import convert_pdf_to_jpg, resize_image
from app.calculations.volume_calculator import calculate_volume
from .tools import generate_unique_filename, save_file, remove_file


router = APIRouter()
logger = logging.getLogger(__name__)

async def process_drawing_file(file: UploadFile, current_user_id: int) -> tuple:
    """
    处理上传的图纸文件，返回处理后的文件信息
    
    Args:
        file: 上传的文件
        current_user_id: 当前用户ID
    
    Returns:
        tuple: (file_name, original_filename, jpg_path, file_url)
    """
    try:
        original_filename = file.filename
        # 使用现有的生成唯一文件名的函数
        file_name = generate_unique_filename(original_filename)
        base_filename = os.path.splitext(file_name)[0]  # 不带扩展名的文件名
        
        # 创建必要的目录
        upload_dir = f"drawings_upload/{current_user_id}"
        output_dir = f"{upload_dir}/converted"
        os.makedirs(upload_dir, exist_ok=True)
        os.makedirs(output_dir, exist_ok=True)

        # 保存原始文件
        original_file_path = os.path.join(upload_dir, file_name)
        save_file(original_file_path, file)

        # 根据文件类型进行转换
        if file_name.lower().endswith('.pdf'):
            # 修改 convert_pdf_to_jpg 函数调用，使用相同的文件名格式
            jpg_path = os.path.join(output_dir, f"{base_filename}.jpg")
            images = convert_from_path(original_file_path)
            if not images:
                raise ValueError("No images extracted from PDF")
            images[0].save(jpg_path, 'JPEG')
        # elif file_name.lower().endswith('.png'):
        #     jpg_path = os.path.join(output_dir, f"{base_filename}.jpg")
        #     with Image.open(original_file_path) as im:
        #         rgb_im = im.convert('RGB')
        #         rgb_im.save(jpg_path)
        
        elif file_name.lower().endswith('.png'):
            jpg_path = os.path.join(output_dir, f"{base_filename}.jpg")
            # 使用 PIL 打开图像并保持方向
            with Image.open(original_file_path) as im:
                # 获取 EXIF 数据
                try:
                    exif = im.info.get('exif', None)
                except:
                    exif = None
                
                # 转换为 RGB
                rgb_im = im.convert('RGB')
                
                # 如果有 EXIF 数据，保持原有方向
                if exif:
                    rgb_im.save(jpg_path, 'JPEG', exif=exif, quality=95)
                else:
                    # 如果没有 EXIF 数据，直接保存
                    rgb_im.save(jpg_path, 'JPEG', quality=95)
        else:
            # 对于其他格式（如已经是jpg），直接使用原始文件
            jpg_path = original_file_path

        # 调整图片大小
        resize_image(jpg_path)
        
        # 生成文件URL
        file_url = f"http://{settings.DOMAIN}:8091/{jpg_path}"
        
        return file_name, original_filename, jpg_path, file_url

    except Exception as e:
        logger.error(f"Error processing file {file.filename}: {str(e)}")
        raise HTTPException(
            status_code=400, 
            detail=f"File processing error: {str(e)}"
        )

@router.post("/upload", response_model=ResponseBase)
async def upload_drawing(
    session: SessionDep,
    current_user: CurrentUser,
    project_id: int = Form(...),
    file: UploadFile = File(...),
    category: str = Form(...),
    discipline: str = Form(...),
    description: str = Form(None),
) -> Any:
    """
    统一的图纸上传接口
    """
    try:
        # 处理文件
        file_name, original_filename, jpg_path, file_url = await process_drawing_file(
            file, current_user.id
        )

        # 创建图纸记录
        drawing = Drawing(
            file_name=file_name,
            original_filename=original_filename,
            file_path=jpg_path,
            file_url=file_url,
            category=category,
            discipline=discipline,
            description=description,
            user_id=current_user.id,
            project_id=project_id,
            uploaded_at=da.now(timezone("Asia/Shanghai")).isoformat(),
        )
        
        session.add(drawing)
        session.commit()
        session.refresh(drawing)

        return ResponseBase(
            code=200,
            message="Drawing uploaded successfully",
            data=DrawingPublic(**drawing.dict())
        )
    except Exception as e:
        logger.error(f"Error uploading drawing: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/{project_id}/drawinglist", response_model=ResponseBase)
def read_items(
    project_id: int,
    session: SessionDep,
    current_user: CurrentUser,
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    获取绘图列表。

    参数:
    - project_id: 项目ID
    - session: 数据库会话
    - current_user: 当前用户
    - skip: 跳过的记录数
    - limit: 返回的记录数

    返回:
    - DrawingPublic: 包含绘图列表和总数的对象
    """
    if current_user.is_superuser:
        count_statement = select(func.count()).select_from(Drawing)
        count = session.exec(count_statement).one()
        statement = select(Drawing).offset(skip).limit(limit)
        drawings = session.exec(statement).all()
    else:
        count_statement = (
            select(func.count())
            .select_from(Drawing)
            .where(Drawing.project_id == project_id)
        )
        count = session.exec(count_statement).one()
        statement = (
            select(Drawing)
            .where(Drawing.project_id == project_id)
            .offset(skip)
            .limit(limit)
        )
        drawings = session.exec(statement).all()

    if drawings is None:
        return ResponseBase(code=404, message="Drawings not found")
    return ResponseBase(
        code=200, message="Success", data={"drawings": drawings, "count": count}
    )
    
@router.get("/drawing/{drawing_id}", response_model=ResponseBase)
def get_drawing_detail(
    drawing_id: int,
    session: SessionDep,
    current_user: CurrentUser,
) -> Any:
    """
    获取指定绘图的详细信息。

    参数:
    - drawing_id: 图纸ID
    - session: 数据库会话
    - current_user: 当前用户

    返回:
    - DrawingPublic: 返回图纸的详细信息
    """
    # 查询绘图信息
    drawing = session.get(Drawing, drawing_id)
    if not drawing:
        raise HTTPException(status_code=404, detail="Drawing not found")

    # 检查用户权限
    if not current_user.is_superuser and drawing.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    # 返回绘图详情
    return ResponseBase(
        code=200,
        message="Success",
        data=drawing
    )

@router.delete("/{drawing_id}")
def delete_drawing(session: SessionDep, current_user: CurrentUser, drawing_id: int) -> ResponseBase:
    """
    删除图纸
    
    参数:
    - drawing_id: 图纸ID
    
    返回:
    - ResponseBase: 返回删除结果
    """
    try:
        drawing = session.get(Drawing, drawing_id)
        if not drawing:
            raise HTTPException(status_code=404, detail="Drawing not found")
        
        # 先将 latest_recognition_task_id 设为 None，以解除外键约束
        drawing.latest_recognition_task_id = None
        session.add(drawing)
        session.commit()
        
        # 删除与该绘图关联的 recognition_tasks 中的记录
        recognition_tasks_statement = select(RecognitionTask).where(RecognitionTask.drawing_id == drawing_id)
        recognition_tasks = session.exec(recognition_tasks_statement).all()
        for task in recognition_tasks:
            session.delete(task)
        
        # 删除绘图
        session.delete(drawing)
        
        session.commit()
        return ResponseBase(code=200, message="Drawing and its references deleted successfully")
    
    except SQLAlchemyError as e:
        session.rollback()
        raise HTTPException(status_code=500, detail="Internal Server Error") from e
    

@router.post("/recognize", response_model=ResponseBase)
def recognize_drawings(
    *,
    request: RecognizeRequest,
    session: SessionDep,
    current_user: CurrentUser,
) -> Any:
    """
    触发图纸组件任务
    
    参数:
    - request: 请求参数
    - session: 数据库会话
    - current_user: 当前用户
    
    返回:
    - ResponseBase: 返回任务状态
    """
    # 1. 验证图纸是否存在
    drawing_statement = select(Drawing).where(Drawing.id == request.drawing_id)
    drawing = session.exec(drawing_statement).first()
    if not drawing:
        raise HTTPException(status_code=404, detail="Drawing not found")

    # 2. 创建识别任务记录
    recognition_task = RecognitionTask(
        drawing_id=drawing.id,
        status="PENDING",
        created_by=current_user.id
    )
    session.add(recognition_task)
    session.commit()
    session.refresh(recognition_task)

    # 3. 触发 Celery 工作流
    task = recognize_drawing_components.delay(drawing.file_path, drawing.id, drawing.category)

    # 4. 更新任务状态
    recognition_task.celery_task_id = task.id
    drawing.latest_recognition_task_id = recognition_task.id
    session.commit()
    
    # 5. 更新 Drawing 的 latest_recognition_task_id 字段
    drawing.latest_recognition_task_id = recognition_task.id
    session.add(drawing)
    session.commit()

    return ResponseBase(
        code=200, 
        message="Recognition task created successfully", 
        data={
            "task_id": recognition_task.id,
            "status": recognition_task.status
        }
    )
    
@router.get("/recognize/{task_id}", response_model=ResponseBase)
def get_recognition_status(
    task_id: int,
    session: SessionDep,
    current_user: CurrentUser,
) -> Any:
    """
    获取识别任务状态
    
    参数:
    - task_id: 任务ID
    - session: 数据库会话
    - current_user: 当前用户
    
    返回:
    - ResponseBase: 返回任务状态
    """
    #依据task_id查询对应的drawing表中的finally_result字段
    drawing = session.exec(select(Drawing).where(Drawing.latest_recognition_task_id == task_id)).first()
    #判断drawing表中的finally_result字段是否为空,为空则继续查询下面的task表，如果有值则直接返回
    if drawing.finally_result:
        return ResponseBase(
        code=200,
        message="Task status retrieved successfully",
        data={
            "task_id": task_id,
            "status": "COMPLETED",
            "result": drawing.finally_result
        }
    )
    # 查询 RecognitionTask
    task_statement = select(RecognitionTask).where(RecognitionTask.id == task_id)
    task = session.exec(task_statement).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 获取 Celery 任务状态
    try:
        celery_task = recognize_drawing_components.AsyncResult(task.celery_task_id)
    except Exception as e:
        logger.error(f"Failed to fetch Celery task status for task {task_id}: {str(e)}")
        return ResponseBase(
            code=500,
            message="Error fetching task status",
            data={"task_id": task_id, "error": str(e)}
        )
        
    # 根据 Celery 任务状态更新数据库并返回
    if celery_task.state == "PENDING":
        task.status = "PENDING"
    elif celery_task.state == "SUCCESS":
        task.status = "COMPLETED"
        session.commit()
    elif celery_task.state == "FAILURE":
        task.status = "FAILED"
        session.commit()
    else:
        task.status = celery_task.state

    return ResponseBase(
        code=200,
        message="Task status retrieved successfully",
        data={
            "task_id": task.id,
            "status": task.status,
            "created_at": task.created_at,
            "completed_at": task.completed_at,
            "result": task.result if task.status == "COMPLETED" else None
        }
    )
    
@router.post("/update_drawing_result", response_model=ResponseBase)
def update_drawing_result(
    request: UpdateDrawingResultRequest,
    session: SessionDep,
    current_user: CurrentUser,
) -> Any:
    """
    更新图纸最终结果
    
    参数:
    - request: 请求参数
    - session: 数据库会话
    - current_user: 当前用户
    
    返回:
    - ResponseBase: 返回更新结果
    """
    drawing_statement = select(Drawing).where(Drawing.id == request.drawing_id)
    drawing = session.exec(drawing_statement).first()
    if not drawing:
        raise HTTPException(status_code=404, detail="Drawing not found")
    
    # 替换table内容
    new_table = drawing.finally_result["table"].copy()
    new_table[request.dataIndex] = request.data
    
    drawing.finally_result = {
        **drawing.finally_result,
        "table": new_table
    }
    session.add(drawing)
    session.commit()   
    
    
    # drawing.finally_result["table"] = new_table
    # session.commit()

    return ResponseBase(
        code=200,
        message="Drawing result updated successfully",
        data={
            "drawing_id": drawing.id
        }
    )
    

@router.post("/get_drawing_result", response_model=ResponseBase)
def get_drawing_result(
    request: GetDrawingResultRequest,
    session: SessionDep,
    current_user: CurrentUser,
) -> Any:
    """
    获取图纸识别结果
    
    参数:
    - request: 请求参数
    - session: 数据库会话
    - current_user: 当前用户
    
    返回:
    - ResponseBase: 返回识别结果
    """
    drawing_statement = select(Drawing).where(Drawing.id == request.drawing_id)
    drawing = session.exec(drawing_statement).first()
    if not drawing:
        raise HTTPException(status_code=404, detail="Drawing not found")

    return ResponseBase(
        code=200,
        message="获取识别结果成功",
        data={
            "drawing_id": drawing.id,
            "finally_result": drawing.finally_result
        }
    )

    
@router.post("/recognition-result", response_model=ResponseBase)
def update_recognition_result(
    *,
    result: dict,
    session: SessionDep,
) -> Any:
    """
    更新识别结果
    
    参数:
    - result: 识别结果
    - session: 数据库会话
    
    返回:
    - ResponseBase: 返回更新结果
    """
    drawing_id = result.get("drawing_id")
    status = result.get("status")

    drawing_statement = select(Drawing).where(Drawing.id == drawing_id)
    drawing = session.exec(drawing_statement).first()
    if not drawing:
        raise HTTPException(status_code=404, detail="Drawing not found")

    task_statement = select(RecognitionTask).where(RecognitionTask.drawing_id == drawing_id)
    task = session.exec(task_statement).first()
    if not task:
        raise HTTPException(status_code=404, detail="Recognition task not found")

    if status == "success":
        drawing.recognized_components = result.get("components")
        drawing.processed_image_path = result.get("image_path")
        task.status = "COMPLETED"
        task.completed_at = da.utcnow()
    else:
        task.status = "FAILED"
        task.error_message = result.get("error")

    session.commit()

    return ResponseBase(
        code=200,
        message="Recognition result updated successfully",
        data={
            "task_id": task.id,
            "status": task.status
        }
    )

@router.get("/export/{drawing_id}", response_class=FileResponse)
def export_drawing_result(
    drawing_id: int,
    session: SessionDep,
    current_user: CurrentUser,
    background_tasks: BackgroundTasks,
) -> Any:
    """
    导出图纸识别结果到Excel文件
    
    参数:
    - drawing_id: 图纸ID
    - session: 数据库会话
    - current_user: 当前用户
    - background_tasks: 后台任务
    
    返回:
    - FileResponse: 返回Excel文件
    """
    drawing_statement = select(Drawing).where(Drawing.id == drawing_id)
    drawing = session.exec(drawing_statement).first()
    if not drawing:
        raise HTTPException(status_code=404, detail="Drawing not found")
    
    if not drawing.finally_result or "table" not in drawing.finally_result:
        raise HTTPException(status_code=400, detail="No result data available for export")
    
    # 创建一个新的工作簿并选择活动的工作表
    wb = Workbook()
    ws = wb.active
    ws.title = "识别结果表"

    # 获取表格数据
    table_data = drawing.finally_result["table"][0]  # 获取第一个（也是唯一的）表格数据
    
    # 获取表头，并排除"位置坐标"
    headers = [header for header in table_data["columns"] if header != "位置坐标"]
    
    # 写入表头
    for col, header in enumerate(headers, start=1):
        ws.cell(row=1, column=col, value=header)

    # 写入数据，排除"位置坐标"列和全是"-"的行
    coord_index = table_data["columns"].index("位置坐标")
    excel_row = 2  # 从第2行开始写入数据
    for item in table_data["data"]:
        # 检查是否所有值都是"-"（除了位置坐标）
        if all(value == "-" for i, value in enumerate(item) if i != coord_index):
            continue  # 跳过全是"-"的行
        
        col = 1
        for i, value in enumerate(item):
            if i != coord_index:
                ws.cell(row=excel_row, column=col, value=value)
                col += 1
        excel_row += 1  # 只有在实际写入行后才增加Excel的行号

    # 调整列宽
    for col in range(1, len(headers) + 1):
        ws.column_dimensions[get_column_letter(col)].width = 15

    # 保存到临时文件
    with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp:
        wb.save(tmp.name)
        tmp_path = tmp.name

    # 生成安全的文件名
    safe_filename = re.sub(r'[^a-zA-Z0-9_\-]', '_', drawing.file_name.rsplit('.', 1)[0])
    filename = f"{safe_filename}_result.xlsx"

    # 添加后台任务以删除临时文件
    background_tasks.add_task(remove_file, tmp_path)

    return FileResponse(
        tmp_path,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        filename=filename,
        background=background_tasks
    )

@router.post("/update_grid", response_model=ResponseBase)
def update_grid(
    request: GridUpdateRequest,
    session: SessionDep,
    current_user: CurrentUser,
) -> Any:
    """
    更新或创建图纸网格数据
    
    参数:
    - request: 请求参数
    - session: 数据库会话
    - current_user: 当前用户
    
    返回:
    - ResponseBase: 返回更新结果
    """
    if request.drawing_id:
        # 更新现有记录
        drawing = session.get(Drawing, request.drawing_id)
        if not drawing:
            raise HTTPException(status_code=404, detail="Drawing not found")
    else:
        # 创建新记录
        drawing = Drawing(user_id=current_user.id)
        session.add(drawing)

    # 更新 grid 数据
    drawing.grid_data = request.grid.model_dump()

    session.commit()
    session.refresh(drawing)

    return ResponseBase(
        code=200,
        message="Grid data updated successfully",
        data={
            "drawing_id": drawing.id
        }
    )
    

@router.post("/calculate_volume/{drawing_id}", response_model=ResponseBase)
def calculate_drawing_volume(
    drawing_id: int,
    session: SessionDep,
    current_user: CurrentUser,
) -> Any:
    """
    计算图纸体积并更新结果
    """
    logger.info(f"开始处理图纸ID: {drawing_id}")

    # 查询drawing
    drawing = session.get(Drawing, drawing_id)
    if not drawing:
        logger.error(f"未找到图纸ID: {drawing_id}")
        raise HTTPException(status_code=404, detail="Drawing not found")

    # 打印关键参数
    logger.info(f"Category: {drawing.category}")
    logger.info(f"Discipline: {drawing.discipline}")
    logger.info(f"Drawing Set ID: {drawing.drawing_set_id}")
    
    # 获取finally_result
    finally_result = drawing.finally_result
    if not finally_result or "table" not in finally_result:
        logger.error(f"图纸ID {drawing_id} 没有可用的结果数据")
        raise HTTPException(status_code=400, detail="No result data available for calculation")

    try:
        # 获取grid_data（如有）
        grid_data = drawing.grid_data if hasattr(drawing, 'grid_data') else None
        
        # 调用计算模块
        result = calculate_volume(
            category=drawing.category,
            discipline=drawing.discipline,
            table_data=finally_result["table"],
            grid_data=grid_data,
            session=session,
            drawing_set_id=drawing.drawing_set_id
        )

        # 更新finally_result，添加新的表格
        finally_result["table"].extend(result["new_tables"])

        # 更新数据库
        try:
            sql = text("UPDATE drawing SET finally_result = :finally_result WHERE id = :id")
            session.execute(sql, {"finally_result": json.dumps(finally_result), "id": drawing_id})
            session.commit()
            logger.info(f"成功更新数据库，图纸ID: {drawing_id}")
        except Exception as e:
            logger.error(f"更新数据库失败，图纸ID: {drawing_id}, 错误: {str(e)}")
            session.rollback()
            raise HTTPException(status_code=500, detail="Failed to update database")

        return ResponseBase(
            code=200,
            message="Volume calculated and result updated successfully",
            data={
                "drawing_id": drawing.id,
                "volume_results": result["volume_results"]
            }
        )

    except ValueError as e:
        logger.error(f"计算体积时发生错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"计算体积时发生未知错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error calculating volume: {str(e)}")
    


# @router.post("/calculate_volume/{drawing_id}", response_model=ResponseBase)
# def calculate_volume(
#     drawing_id: int,
#     session: SessionDep,
#     current_user: CurrentUser,
# ) -> Any:
#     """
#     计算体积并更新drawing的finally_result
#     """
#     logger.info(f"开始处理图纸ID: {drawing_id}")

#     # 查询drawing
#     drawing = session.get(Drawing, drawing_id)
#     if not drawing:
#         logger.error(f"未找到图纸ID: {drawing_id}")
#         raise HTTPException(status_code=404, detail="Drawing not found")

#     # 获取finally_result
#     finally_result = drawing.finally_result
#     if not finally_result or "table" not in finally_result:
#         logger.error(f"图纸ID {drawing_id} 没有可用的结果数据")
#         raise HTTPException(status_code=400, detail="No result data available for calculation")

#     # 获取table数据
#     table = finally_result["table"][0]
#     data = table["data"]
#     logger.info(f"原始数据: {json.dumps(data[:5], ensure_ascii=False)}")

#     # 计算体积
#     volume_results = {}
#     for row in data:
#         try:
#             component_type, _, length, area, _, _ = row
#             if length != "-" and area != "-" and area != "":
#                 try:
#                     length = float(length)
#                     width, height = map(float, area.split('x'))
#                     volume = length * width * height / 1e9  # 转换为立方米
#                     if component_type not in volume_results:
#                         volume_results[component_type] = 0
#                     volume_results[component_type] += volume
#                     # 更新原始数据中的体积字段
#                     row[4] = f"{volume:.3f}"
#                 except ValueError:
#                     logger.warning(f"无法计算体积，跳过该行: {json.dumps(row, ensure_ascii=False)}")
#                     continue
#         except Exception as e:
#             logger.warning(f"处理行时出错，跳过该行: {json.dumps(row, ensure_ascii=False)}. 错误: {str(e)}")
#             continue

#     logger.info(f"计算的体积结果: {json.dumps(volume_results, ensure_ascii=False)}")

#     # 创建新的算量结果表
#     volume_table = {
#         "title": "算量结果",
#         "columns": ["构件类型", "总体积(m³)"],
#         "data": [[k, f"{v:.3f}"] for k, v in volume_results.items()]
#     }

#     # 更新finally_result
#     finally_result["table"] = [table, volume_table]
#     logger.info(f"更新后的finally_result: {json.dumps(finally_result, ensure_ascii=False)}")

#     # 更新数据库
#     try:
#         # 使用原生SQL更新，以确保数据被正确写入
#         sql = text("UPDATE drawing SET finally_result = :finally_result WHERE id = :id")
#         session.execute(sql, {"finally_result": json.dumps(finally_result), "id": drawing_id})
#         session.commit()
#         logger.info(f"成功更新数据库，图纸ID: {drawing_id}")
#     except Exception as e:
#         logger.error(f"更新数据库失败，图纸ID: {drawing_id}, 错误: {str(e)}")
#         session.rollback()
#         raise HTTPException(status_code=500, detail="Failed to update database")

#     return ResponseBase(
#         code=200,
#         message="Volume calculated and result updated successfully",
#         data={
#             "drawing_id": drawing.id,
#             "volume_results": volume_results
#         }
#     )
