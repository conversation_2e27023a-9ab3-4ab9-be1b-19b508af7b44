from typing import Any, Dict
from fastapi import APIRouter, HTTPException
from pathlib import Path
import json

from app.api.deps import CurrentUser, SessionDep
from app.api.response_schemas import ResponseBase

router = APIRouter()

# 配置文件路径
CONFIG_FILE = Path("config.json")

# 初始配置
DEFAULT_CONFIG = {
    "categories": [
        {
            "name": "设备安装",
            "subcategories": ["门窗", "楼梯", "其他"]
        },
        {
            "name": "建筑工程",
            "subcategories": ["梁", "板", "柱", "墙", "独立基础", "其他"]
        }
    ]
}

def load_config() -> Dict[str, Any]:
    """加载配置文件，如果不存在则创建默认配置"""
    if not CONFIG_FILE.exists():
        CONFIG_FILE.write_text(json.dumps(DEFAULT_CONFIG, ensure_ascii=False, indent=2))
        return DEFAULT_CONFIG
    return json.loads(CONFIG_FILE.read_text())

def save_config(config: Dict[str, Any]) -> None:
    """保存配置到文件"""
    CONFIG_FILE.write_text(json.dumps(config, ensure_ascii=False, indent=2))

@router.get("/", response_model=ResponseBase)
async def get_categories() -> Any:
    """获取分类配置"""
    try:
        config = load_config()
        return ResponseBase(
            code=200,
            message="Success",
            data=config
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/", response_model=ResponseBase)
async def update_categories(
    config: Dict[str, Any]
) -> Any:
    """更新分类配置"""
    try:
        # 简单的格式验证
        if "categories" not in config:
            raise HTTPException(
                status_code=400,
                detail="Invalid config format. Must contain 'categories' key"
            )
        
        # 保存配置
        save_config(config)
        
        return ResponseBase(
            code=200,
            message="Categories updated successfully",
            data=config
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))