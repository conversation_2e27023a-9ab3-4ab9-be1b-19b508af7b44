from typing import List, Optional, Tuple
from pydantic import BaseModel
from enum import Enum

class DrawingType(str, Enum):
    """图纸类型枚举"""
    FOUNDATION_LAYOUT = "foundation_layout"          # 基础布置图
    FOUNDATION_DETAIL_PLAN = "foundation_detail_plan"  # 基础详图平面图
    FOUNDATION_SECTION = "foundation_section"        # 基础剖面图

    @classmethod
    def get_chinese_name(cls, value: str) -> str:
        """获取图纸类型的中文名称"""
        names = {
            cls.FOUNDATION_LAYOUT: "基础布置图",
            cls.FOUNDATION_DETAIL_PLAN: "基础详图平面图",
            cls.FOUNDATION_SECTION: "基础剖面图",
        }
        return names.get(cls(value), "未知类型")

class GetDrawingResultRequest(BaseModel):
    drawing_id: int

class Component(BaseModel):
    label: str
    class_id: int
    confidence: float
    coordinates: List[float]

class UpdateDrawingResultRequest(BaseModel):
    drawing_id: int
    dataIndex: int
    data: dict

class RecognizeRequest(BaseModel):
    drawing_id: int
    
# 定义请求模型
class GridData(BaseModel):
    coordinates: list
    length: str

class GridUpdateRequest(BaseModel):
    grid: GridData
    drawing_id: Optional[int] = None
    
class DrawingSetCreate(BaseModel):
    name: str
    description: Optional[str] = None
    drawing_ids: List[int]
    # drawing_types: Optional[List[DrawingType]] = None

class DrawingSetUpdateDrawings(BaseModel):
    add_drawings: Optional[List[Tuple[int, DrawingType]]] = None  # [(drawing_id, type)]
    remove_drawing_ids: Optional[List[int]] = None