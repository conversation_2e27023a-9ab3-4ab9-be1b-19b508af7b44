from typing import Any, Optional
from pydantic import BaseModel


class ResponseBase(BaseModel):
    code: int
    message: str
    data: Optional[Any] = None


class SuccessResponse(ResponseBase):
    code: int = 200
    message: str = "Success"


class ErrorResponse(ResponseBase):
    code: int
    message: str


class LoginSuccessResponse(SuccessResponse):
    data: Optional[Any] = None


class LoginErrorResponse(ErrorResponse):
    code: int = 401
    message: str = "Invalid username or password"


class ProjectNotFoundError(ErrorResponse):
    code: int = 404
    message: str = "Project not found"


class DrawingNotFoundError(ErrorResponse):
    code: int = 404
    message: str = "Drawing not found"


class InsufficientPermissionsError(ErrorResponse):
    code: int = 403
    message: str = "Insufficient permissions"


class InvalidInputError(ErrorResponse):
    code: int = 400
    message: str = "Invalid input"


class InternalServerError(ErrorResponse):
    code: int = 500
    message: str = "Internal server error"
