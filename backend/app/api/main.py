from fastapi import APIRouter

from app.api.routes import (
    items, 
    login, 
    users, 
    utils, 
    projects, 
    drawings, 
    celery_tasks, 
    category_conf,
    drawingsets
)

api_router = APIRouter()
api_router.include_router(login.router, tags=["login"])
# api_router.include_router(workbench.router, tags=["workbench"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
# api_router.include_router(utils.router, prefix="/utils", tags=["utils"])
# api_router.include_router(items.router, prefix="/items", tags=["items"])
api_router.include_router(projects.router, prefix="/projects", tags=["projects"])
api_router.include_router(drawings.router, prefix="/drawings", tags=["drawings"])
api_router.include_router(drawingsets.router, prefix="/drawing-sets", tags=["drawing-sets"])
api_router.include_router(celery_tasks.router, prefix="/tasks", tags=["tasks"])
api_router.include_router(category_conf.router, prefix="/category_conf", tags=["category_conf"])