from typing import Optional
from datetime import datetime
from sqlmodel import Field, SQLModel, Relationship


class QuantityResult(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    drawing_id: int = Field(foreign_key="drawing.id", unique=True)
    component_type: str
    quantity: float
    unit_price: float
    total_price: float
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    drawing: Optional["Drawing"] = Relationship(back_populates="quantity_result")
