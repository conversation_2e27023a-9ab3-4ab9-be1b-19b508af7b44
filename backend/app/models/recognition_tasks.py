from sqlmodel import SQLModel, Field, Relationship, Column, JSO<PERSON>
from datetime import datetime
from typing import Optional, Dict, Any
from .enums import RecognitionTaskStatus

class RecognitionTask(SQLModel, table=True):
    __tablename__ = "recognition_tasks"

    id: Optional[int] = Field(default=None, primary_key=True)
    drawing_id: int = Field(foreign_key="drawing.id", index=True)
    drawing: Optional["Drawing"] = Relationship(back_populates="recognition_tasks")
    status: RecognitionTaskStatus = Field(default=RecognitionTaskStatus.PENDING, index=True)
    celery_task_id: Optional[str] = Field(default=None, index=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    created_by: int = Field(foreign_key="user.id", index=True)
    creator: Optional["User"] = Relationship(back_populates="created_recognition_tasks")
    
    # Needed for Column(JSON)
    class Config:
        arbitrary_types_allowed = True
