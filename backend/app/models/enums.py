from enum import Enum

class DrawingStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class RecognitionTaskStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class UserRole(str, Enum):
    USER = "user"
    ADMIN = "admin"
    SUPERUSER = "superuser" 
    
class DrawingType(str, Enum):
    """图纸类型枚举"""
    FOUNDATION_LAYOUT = "foundation_layout"          # 基础布置图
    FOUNDATION_DETAIL_PLAN = "foundation_detail_plan"  # 基础详图平面图
    FOUNDATION_SECTION = "foundation_section"        # 基础剖面图

    @classmethod
    def get_chinese_name(cls, value: str) -> str:
        """获取图纸类型的中文名称"""
        names = {
            cls.FOUNDATION_LAYOUT: "基础布置图",
            cls.FOUNDATION_DETAIL_PLAN: "基础详图平面图",
            cls.FOUNDATION_SECTION: "基础剖面图",
        }
        return names.get(cls(value), "未知类型")