from typing import List, Optional
from sqlmodel import SQLModel, Field, Relationship
from .enums import UserRole


# Shared properties
class UserBase(SQLModel):
    email: str = Field(unique=True, index=True)
    nickname: Optional[str] = None
    role: UserRole = Field(default=UserRole.USER)
    is_active: bool = Field(default=True)
    is_superuser: bool = Field(default=False)
    full_name: Optional[str] = None


# Properties to receive via API on creation
class UserCreate(UserBase):
    password: str


# TODO replace email str with EmailStr when sqlmodel supports it
class UserRegister(SQLModel):
    email: str
    password: str
    full_name: str | None = None


# Properties to receive via API on update, all are optional
class UserUpdate(UserBase):
    email: Optional[str] = None
    password: Optional[str] = None


class UserUpdateMe(SQLModel):
    full_name: Optional[str] = None
    email: Optional[str] = None


class UpdatePassword(SQLModel):
    current_password: str
    new_password: str


# Database model, database table inferred from class name
class User(UserBase, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    hashed_password: str
    items: List["Item"] = Relationship(back_populates="owner")
    projects: List["Project"] = Relationship(back_populates="owner")
    drawings: List["Drawing"] = Relationship(back_populates="user")
    created_recognition_tasks: Optional[List["RecognitionTask"]] = Relationship(back_populates="creator")
    created_drawing_sets: List["DrawingSet"] = Relationship(back_populates="creator")

# Properties to return via API, id is always required
class UserPublic(UserBase):
    id: int


class UsersPublic(SQLModel):
    data: List[UserPublic]
    count: int


# Shared properties for items
class ItemBase(SQLModel):
    title: str
    description: Optional[str] = None


# Properties to receive on item creation
class ItemCreate(ItemBase):
    title: str


# Properties to receive on item update
class ItemUpdate(ItemBase):
    title: Optional[str] = None


# Database model for items
class Item(ItemBase, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    title: str
    owner_id: Optional[int] = Field(default=None, foreign_key="user.id")
    owner: Optional[User] = Relationship(back_populates="items")


# Properties to return via API for items
class ItemPublic(ItemBase):
    id: int
    owner_id: int


class ItemsPublic(SQLModel):
    data: List[ItemPublic]
    count: int


# Generic message model
class Message(SQLModel):
    message: str


# JSON payload containing access token
class Token(SQLModel):
    access_token: str
    token_type: str = "bearer"


# Contents of JWT token
class TokenPayload(SQLModel):
    sub: Optional[int] = None


class NewPassword(SQLModel):
    token: str
    new_password: str


class LoginToken(SQLModel):
    tokeninfo: Token
    userinfo: User


class LoginData(SQLModel):
    email: str
    password: str
