from typing import Optional, Dict, Any
from sqlmodel import SQLModel, Field, Relationship, Column, JSON


class ComponentBase(SQLModel):
    name: str
    type: str
    attributes: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSON))


class ComponentCreate(ComponentBase):
    pass


class ComponentRead(ComponentBase):
    id: int


class Component(ComponentBase, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    drawing_id: Optional[int] = Field(default=None, foreign_key="drawing.id")
    drawing: Optional["Drawing"] = Relationship(back_populates="components")
