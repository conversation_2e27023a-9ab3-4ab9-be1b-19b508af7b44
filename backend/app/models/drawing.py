from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlmodel import SQLModel, Field, Relationship, Column, JSON
from .enums import DrawingStatus, DrawingType


# Shared properties for drawings
class DrawingBase(SQLModel):
    file_name: str
    file_path: str
    file_url: Optional[str] = None
    category: str = Field(default="")
    description: Optional[str] = None
    uploaded_at: Optional[datetime] = None
    status: DrawingStatus = Field(default=DrawingStatus.PENDING)


# Properties to receive on drawing creation
class DrawingCreate(DrawingBase):
    pass


# Properties to receive on drawing update
class DrawingUpdate(DrawingBase):
    file_name: Optional[str] = None
    file_path: Optional[str] = None
    category: Optional[str] = None
    description: Optional[str] = None
    updated_at: Optional[datetime] = None
    version: Optional[str] = None
    status: Optional[DrawingStatus] = None


# Database model for drawings
class Drawing(DrawingBase, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    original_filename: str = Field(default="")
    user_id: int = Field(foreign_key="user.id", index=True)
    user: Optional["User"] = Relationship(back_populates="drawings")
    project_id: Optional[int] = Field(default=None, foreign_key="project.id", index=True)
    project: Optional["Project"] = Relationship(back_populates="drawings")
    discipline: str = Field(default="设备安装")
    
    grid_data: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    # table_cut: Optional[dict] = Field(default=None, sa_column=Column(JSON))
    origin_result: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    finally_result: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    components: List["Component"] = Relationship(back_populates="drawing")
    quantity_result: Optional["QuantityResult"] = Relationship(back_populates="drawing")
    
    # 套图相关字段
    drawing_set_id: Optional[int] = Field(default=None, foreign_key="drawing_sets.id", index=True)
    drawing_type: Optional[DrawingType] = None  # 在套图中的类型
    drawing_set: Optional["DrawingSet"] = Relationship(back_populates="drawings")
    recognition_tasks: List["RecognitionTask"] = Relationship(back_populates="drawing")
    # latest_recognition_task_id: Optional[int] = Field(default=None, foreign_key="recognition_tasks.id", index=True)
    latest_recognition_task_id: Optional[int] = Field(default=None, index=True)

    class Config:
        arbitrary_types_allowed = True


# Properties to return via API for drawings
class DrawingPublic(DrawingBase):
    id: int
    file_path: str
    original_filename: Optional[str] = ""
    project_id: int


class DrawingsPublic(SQLModel):
    data: List[DrawingPublic]
    count: int
