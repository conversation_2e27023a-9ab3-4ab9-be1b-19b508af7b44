from datetime import datetime as da
from typing import List, Optional
from sqlmodel import SQLModel, Field, Relationship


# Shared properties for projects
class ProjectBase(SQLModel):
    name: str
    description: Optional[str] = None
    created_at: Optional[da] = None


# Properties to receive on project creation
class ProjectCreate(ProjectBase):
    pass


# Properties to receive on project update
class ProjectUpdate(ProjectBase):
    name: Optional[str] = None
    description: Optional[str] = None


# Database model for projects
class Project(ProjectBase, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    owner_id: Optional[int] = Field(default=None, foreign_key="user.id")
    owner: Optional["User"] = Relationship(back_populates="projects")
    drawings: List["Drawing"] = Relationship(back_populates="project")
    drawing_sets: List["DrawingSet"] = Relationship(back_populates="project")


# Properties to return via API for projects
class ProjectPublic(ProjectBase):
    id: int
    owner_id: int


class ProjectsPublic(SQLModel):
    data: List[ProjectPublic]
    count: int
