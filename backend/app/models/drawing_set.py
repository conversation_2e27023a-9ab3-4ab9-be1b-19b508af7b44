from datetime import datetime
from typing import Optional, List, Dict
from sqlmodel import SQLModel, Field, Relationship, Column, JSON
from .enums import DrawingType
from .drawing import DrawingPublic

class DrawingSetBase(SQLModel):
    """套图基础模型"""
    name: str
    description: Optional[str] = None
    project_id: int = Field(foreign_key="project.id")

class DrawingSet(DrawingSetBase, table=True):
    """套图数据库模型"""
    __tablename__ = "drawing_sets"

    id: Optional[int] = Field(default=None, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: int = Field(foreign_key="user.id")
    updated_at: Optional[datetime] = Field(default=None)
    
    # 记录套图所需的图纸类型及其状态
    required_types: Dict = Field(
        default={
            "foundation_layout": False,
            "foundation_detail_plan": False,
            "foundation_section": False
        },
        sa_column=Column(JSON)
    )
    
    # 关系字段
    drawings: List["Drawing"] = Relationship(back_populates="drawing_set")
    project: Optional["Project"] = Relationship(back_populates="drawing_sets")
    creator: Optional["User"] = Relationship(back_populates="created_drawing_sets")

class DrawingSetCreate(DrawingSetBase):
    """套图创建模型"""
    drawing_ids: List[int]
    drawing_types: List[DrawingType]

class DrawingSetRead(DrawingSetBase):
    """套图读取模型"""
    id: int
    created_at: datetime
    created_by: int
    required_types: Dict
    drawings: List["DrawingPublic"]

    class Config:
        from_attributes = True
        
class DrawingSetResponse(SQLModel):
    """套图响应模型"""
    id: int
    name: str
    project_id: int
    description: Optional[str] = None
    created_at: datetime
    created_by: int
    required_types: Dict
    drawings: List[DrawingPublic]

    class Config:
        from_attributes = True

class DrawingSetPublic(SQLModel):
    """简化的套图信息模型"""
    id: int
    name: str
    project_id: int
    created_at: datetime
    drawings_count: int = 0

    class Config:
        from_attributes = True