import os
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any

import emails  # type: ignore
import jwt
from jinja2 import Template
from jwt.exceptions import InvalidTokenError
from pdf2image import convert_from_path
from PIL import Image, ImageOps

from app.core.config import settings

# 转换pdf为jpg
def convert_pdf_to_jpg(pdf_path: str, output_dir: str, base_filename: str) -> str:
    """
    将PDF转换为JPG
    
    Args:
        pdf_path: PDF文件路径
        output_dir: 输出目录
        base_filename: 基础文件名（不含扩展名）
    
    Returns:
        str: 转换后的JPG文件路径
    """
    images = convert_from_path(pdf_path)
    if not images:
        raise ValueError("No images extracted from PDF")
        
    # 使用传入的基础文件名创建jpg路径
    jpg_path = os.path.join(output_dir, f"{base_filename}.jpg")
    images[0].save(jpg_path, 'JPEG')
    return jpg_path

# def resize_image(image_path, max_size=3072):
#     image = Image.open(image_path)
#     width, height = image.size
#     if max(width, height) > max_size:
#         if width > height:
#             new_width = max_size
#             new_height = int(max_size * height / width)
#         else:
#             new_height = max_size
#             new_width = int(max_size * width / height)
#         image = image.resize((new_width, new_height), Image.LANCZOS)
#         image.save(image_path)

def resize_image(image_path, max_size=3072):
    """
    调整图像大小并保持原始方向
    
    Args:
        image_path: 图像文件路径
        max_size: 最大尺寸（默认3072像素）
    """
    with Image.open(image_path) as image:
        # 保持原始方向
        image = ImageOps.exif_transpose(image)
        
        # 获取原始尺寸
        width, height = image.size
        
        # 检查是否需要调整大小
        if max(width, height) > max_size:
            if width > height:
                new_width = max_size
                new_height = int(max_size * height / width)
            else:
                new_height = max_size
                new_width = int(max_size * width / height)
                
            # 调整大小
            image = image.resize((new_width, new_height), Image.LANCZOS)
            
            # 保存时保持质量
            image.save(image_path, quality=95, optimize=True)


@dataclass
class EmailData:
    html_content: str
    subject: str


def render_email_template(*, template_name: str, context: dict[str, Any]) -> str:
    template_str = (
        Path(__file__).parent / "email-templates" / "build" / template_name
    ).read_text()
    html_content = Template(template_str).render(context)
    return html_content


def send_email(
    *,
    email_to: str,
    subject: str = "",
    html_content: str = "",
) -> None:
    assert settings.emails_enabled, "no provided configuration for email variables"
    message = emails.Message(
        subject=subject,
        html=html_content,
        mail_from=(settings.EMAILS_FROM_NAME, settings.EMAILS_FROM_EMAIL),
    )
    smtp_options = {"host": settings.SMTP_HOST, "port": settings.SMTP_PORT}
    if settings.SMTP_TLS:
        smtp_options["tls"] = True
    elif settings.SMTP_SSL:
        smtp_options["ssl"] = True
    if settings.SMTP_USER:
        smtp_options["user"] = settings.SMTP_USER
    if settings.SMTP_PASSWORD:
        smtp_options["password"] = settings.SMTP_PASSWORD
    response = message.send(to=email_to, smtp=smtp_options)
    logging.info(f"send email result: {response}")


def generate_test_email(email_to: str) -> EmailData:
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - Test email"
    html_content = render_email_template(
        template_name="test_email.html",
        context={"project_name": settings.PROJECT_NAME, "email": email_to},
    )
    return EmailData(html_content=html_content, subject=subject)


def generate_reset_password_email(email_to: str, email: str, token: str) -> EmailData:
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - Password recovery for user {email}"
    link = f"{settings.server_host}/reset-password?token={token}"
    html_content = render_email_template(
        template_name="reset_password.html",
        context={
            "project_name": settings.PROJECT_NAME,
            "username": email,
            "email": email_to,
            "valid_hours": settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS,
            "link": link,
        },
    )
    return EmailData(html_content=html_content, subject=subject)


def generate_new_account_email(
    email_to: str, username: str, password: str
) -> EmailData:
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - New account for user {username}"
    html_content = render_email_template(
        template_name="new_account.html",
        context={
            "project_name": settings.PROJECT_NAME,
            "username": username,
            "password": password,
            "email": email_to,
            "link": settings.server_host,
        },
    )
    return EmailData(html_content=html_content, subject=subject)


def generate_password_reset_token(email: str) -> str:
    delta = timedelta(hours=settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS)
    now = datetime.utcnow()
    expires = now + delta
    exp = expires.timestamp()
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now, "sub": email},
        settings.SECRET_KEY,
        algorithm="HS256",
    )
    return encoded_jwt


def verify_password_reset_token(token: str) -> str | None:
    try:
        decoded_token = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
        return str(decoded_token["sub"])
    except InvalidTokenError:
        return None
