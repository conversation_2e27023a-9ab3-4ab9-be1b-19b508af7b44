from typing import Type
from app.tasks.recognition.base import RecognitionStrategy
from app.tasks.recognition.beam_strategy import BeamRecognitionStrategy
from app.tasks.recognition.column_strategy import ColumnRecognitionStrategy
from app.tasks.recognition.equipment_strategy import EquipmentRecognitionStrategy
from app.tasks.recognition.detail_strategy import DetailRecognitionStrategy
from app.tasks.recognition.basic_layout_strategy import BasicLayoutRecognitionStrategy


class RecognitionStrategyFactory:
    """识别策略工厂"""
    
    _strategies = {
        "beam": BeamRecognitionStrategy,
        "column": ColumnRecognitionStrategy,
        "equipment": EquipmentRecognitionStrategy,
        "梁": BeamRecognitionStrategy,
        "柱": ColumnRecognitionStrategy,
        "其他设备": EquipmentRecognitionStrategy,
        "基础详图": DetailRecognitionStrategy,
        "基础布置图": BasicLayoutRecognitionStrategy,
        # "板": PlateRecognitionStrategy,
        # "其他": OtherRecognitionStrategy
    }
    
    @classmethod
    def get_strategy(cls, drawing_type: str, **kwargs) -> RecognitionStrategy:
        """获取对应的识别策略"""
        strategy_class = cls._strategies.get(drawing_type)
        if not strategy_class:
            raise ValueError(f"Unsupported drawing type: {drawing_type}")
            
        return strategy_class(**kwargs)
        
    @classmethod
    def register_strategy(cls, drawing_type: str, strategy_class: Type[RecognitionStrategy]):
        """注册新的识别策略"""
        cls._strategies[drawing_type] = strategy_class 