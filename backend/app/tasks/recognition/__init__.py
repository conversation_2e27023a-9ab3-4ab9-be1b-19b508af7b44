from app.tasks.recognition.base import RecognitionStrategy
from app.tasks.recognition.beam_strategy import BeamRecognitionStrategy
from app.tasks.recognition.column_strategy import ColumnRecognitionStrategy
from app.tasks.recognition.equipment_strategy import EquipmentRecognitionStrategy
from app.tasks.recognition.detail_strategy import DetailRecognitionStrategy
from app.tasks.recognition.basic_layout_strategy import BasicLayoutRecognitionStrategy


__all__ = [
    'RecognitionStrategy',
    'BeamRecognitionStrategy',
    'ColumnRecognitionStrategy',
    'EquipmentRecognitionStrategy',
    'DetailRecognitionStrategy',
    'BasicLayoutRecognitionStrategy'
]