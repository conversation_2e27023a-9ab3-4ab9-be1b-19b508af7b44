import logging
from typing import Dict, List, Optional, Tuple
from PIL import Image
import os
import json
import subprocess
from pathlib import Path
import httpx
import re

from app.models import Drawing
from app.tasks.recognition.base import RecognitionStrategy
from app.tasks.jc_xl_paired_boxes import extract_paired_boxes
from app.core.config import settings
from app.tasks.recognition.utils.image_processing import split_image, merge_results, stitch_images
from app.tasks.recognition.utils.ocr_processing import merge_text_boxes

logger = logging.getLogger(__name__)

class DetailRecognitionStrategy(RecognitionStrategy):
    """基础-详图识别策略
    1.yolo识别基础详图（参考梁识别程序），识别结果会有 jichu-ping和jichu-pou（平面和剖面）
    2.调用jc_xl_paired_boxes的extract_paired_boxes，返回图像中所有配对的矩形框，格式是：
    ""
    [{'jc-ping': [350, 250, 2169, 1030], 'jc-pou': [960, 377, 1338, 904]}, {'jc-ping': [960, 881, 1338, 1408], 'jc-pou': [1539, 1068, 1725, 1253]}, {'jc-ping': [1535, 1336, 1743, 1689], 'jc-pou': [960, 1372, 1338, 1929]}]
    ""
    3.判断yolo识别框是否在配对框内，如果在，则将yolo框和配对框相关联(一个配对框对应多个yolo框)，组和数据格式为
    [{'jc-ping': [350, 250, 2169, 1030], ping-yolo: [[x1,y1,x2,y2],[x1,y1,x2,y2]], 'jc-pou': [960, 377, 1338, 904], pou-yolo: [[x1,y1,x2,y2],[x1,y1,x2,y2]]},...]
    4.调用ocr接口，把每个配对框的ping面进行ocr，识别一个文本例如“JC-1”，使用正则提取数字字母+"-"+数字的文本（不超过5长度，不含中文），提取一个型号数据，加入到原数据格式中
    5.组合数据，返回结果（参考梁识别程序）
    """
    
    def __init__(self, drawing_id: int, file_path: str, drawing: Drawing):
        super().__init__(drawing_id, file_path, drawing)
        
    @property
    def needs_scale(self) -> bool:
        return True
    
    def preprocess(self) -> None:
        """预处理步骤，验证必要数据"""
        if self.scale is None:
            raise ValueError("未设置比例尺，无法进行梁识别")
        
    # def preprocess(self) -> None:
    #     """预处理步骤"""
    #     if not self.drawing.scale_info:
    #         raise ValueError("Missing scale information")
            
    #     try:
    #         coords, actual_length = self.drawing.scale_info.split(';')
    #         self.scale = get_scale(coords, actual_length)
    #         logger.info(f"计算得到比例尺: {self.scale}")
    #     except Exception as e:
    #         logger.error(f"计算比例尺时出错: {str(e)}")
    #         raise ValueError(f"Invalid scale information: {str(e)}")
            
    def perform_recognition(self) -> Dict:
        """执行识别流程"""
        logger.info(f"开始识别基础详图，图纸ID: {self.drawing_id}")
        try:
            # 检查文件是否存在
            if not Path(self.file_path).exists():
                raise FileNotFoundError(f"File not found: {self.file_path}")
                
            # 切换到 YOLOv9 目录
            os.chdir(self.yolov9_dir)
            logger.info(f"Current working directory: {os.getcwd()}")
            
            # 1. YOLO识别基础详图
            logger.info("开始YOLO识别...")
            yolo_results, result_image_path = self._perform_yolo_detection()
            
            # 2. 获取配对框
            logger.info("开始获取配对框...")
            paired_boxes = extract_paired_boxes(str(self.file_path))
            logger.info(f"配对框结果: {paired_boxes}")
            
            # 3. 关联YOLO框和配对框
            logger.info("开始关联YOLO框和配对框...")
            associated_results = self._associate_boxes(yolo_results, paired_boxes)
            
            # 4. OCR识别
            logger.info("开始OCR识别...")
            final_results = self._perform_ocr_recognition(associated_results)
            
            return {
                "components": final_results,
                "image_path": result_image_path
            }
            
        except Exception as e:
            logger.error(f"基础详图识别过程出错: {str(e)}")
            raise
            
    def _perform_yolo_detection(self) -> Tuple[List[Dict], str]:
        """执行YOLO检测，包含图像切分和结果合并"""
        try:
            # 切分图像
            logger.info("正在切分图纸...")
            original_images, coordinates = split_image(self.file_path)
            logger.info(f"图纸已被切分为 {len(original_images)} 个部分")
            
            # 创建保存目录
            save_dir = self.yolov9_dir / f"split_img/{self.drawing_id}"
            os.makedirs(save_dir, exist_ok=True)
            
            all_yolo_results = []
            result_images = []
            
            # 处理每个切片
            for idx, (image, coord) in enumerate(zip(original_images, coordinates)):
                logger.info(f"正在处理第 {idx+1}/{len(original_images)} 个切分图像")
                
                # 保存切片图像
                split_image_path = save_dir / f"split_image_{idx}.jpg"
                image.save(split_image_path)
                
                # YOLO检测
                try:
                    logger.info(f"正在执行第 {idx+1} 个切分图像的YOLO检测")
                    model_path = settings.YOLO_MODELS["建筑工程_基础"]
                    command = f"python detect.py --source {split_image_path} --img 1024 --weights '{model_path}' --device 0 --name yolov9_c_c_640_val --json"
                    
                    result = subprocess.run(
                        command,
                        shell=True,
                        check=True,
                        capture_output=True,
                        text=True
                    )
                    
                    yolo_output = json.loads(result.stdout)
                    
                    # 合并YOLO结果
                    merged_results = merge_results([yolo_output], [coord], image.size)
                    all_yolo_results.extend(merged_results)
                    
                    # 保存结果图像
                    result_image_path = yolo_output.get('image_path')
                    if result_image_path and os.path.exists(result_image_path):
                        result_images.append((Image.open(result_image_path), coord))
                    else:
                        result_images.append((image, coord))
                        
                except Exception as e:
                    logger.error(f"YOLO检测出错: {str(e)}")
                    result_images.append((image, coord))
                    continue
                    
                finally:
                    # 清理临时文件
                    if os.path.exists(split_image_path):
                        os.remove(split_image_path)
            
            # 拼接结果图像
            original_size = Image.open(self.file_path).size
            stitched_image = stitch_images(
                original_images,
                result_images,
                coordinates,
                original_size
            )
            
            # 保存拼接图像
            save_dir = self.yolov9_dir / 'stitched_image' / str(self.drawing_id)
            os.makedirs(save_dir, exist_ok=True)
            stitched_image_path = save_dir / 'stitched_result.jpg'
            stitched_image.save(stitched_image_path)
            
            # 修改返回的图片URL路径
            image_url = f"http://{settings.DOMAIN}:8091/static/result/{self.drawing_id}/stitched_result.jpg"
            
            return all_yolo_results, image_url
            
        except Exception as e:
            logger.error(f"YOLO检测失败: {str(e)}")
            raise
            
    def _associate_boxes(self, yolo_results: List[Dict], paired_boxes: List[Dict]) -> List[Dict]:
        """关联YOLO框和配对框"""
        logger.info("开始关联框")
        
        def is_inside_box(yolo_box: List[float], ref_box: List[float]) -> bool:
            """判断YOLO框是否在参考框内"""
            x1, y1, x2, y2 = yolo_box
            rx1, ry1, rx2, ry2 = ref_box
            
            # 计算框的中心点
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2
            
            return (rx1 <= center_x <= rx2) and (ry1 <= center_y <= ry2)
        
        associated_results = []
        
        for paired_box in paired_boxes:
            result = {
                'jc-ping': paired_box['jc-ping'],
                'jc-pou': paired_box['jc-pou'],
                'ping-yolo': [],
                'pou-yolo': []
            }
            
            # 检查每个YOLO结果
            for yolo_box in yolo_results:
                coords = yolo_box['coordinates']
                
                # 检查是否在平面框内
                if is_inside_box(coords, paired_box['jc-ping']):
                    result['ping-yolo'].append(coords)
                    
                # 检查是否在剖面框内
                if is_inside_box(coords, paired_box['jc-pou']):
                    result['pou-yolo'].append(coords)
            
            associated_results.append(result)
            
        return associated_results
        
    def _perform_ocr_recognition(self, associated_results: List[Dict]) -> List[Dict]:
        """对平面框进行OCR识别"""
        logger.info("开始OCR识别")
        
        def extract_foundation_mark(text: str) -> Optional[str]:
            """提取基础标记（字母数字-数字格式）"""
            pattern = r'[A-Za-z0-9]+-\d+'
            matches = [
                match.strip() for match in re.findall(pattern, text)
                if len(match.strip()) <= 5
            ]
            return matches[0] if matches else None
        
        # 打开原始图像
        image = Image.open(self.file_path)
        
        for result in associated_results:
            try:
                # 裁剪平面框区域
                x1, y1, x2, y2 = result['jc-ping']
                ping_image = image.crop((x1, y1, x2, y2))
                
                # 保存临时文件
                temp_path = f"/tmp/ping_{self.drawing_id}.jpg"
                ping_image.save(temp_path)
                
                # 发送OCR请求到paddle_ocr服务
                try:
                    with open(temp_path, "rb") as image_file:
                        files = {
                            "file": (
                                "image.jpg",
                                image_file,
                                "image/jpeg"
                            )
                        }
                        headers = {
                            'accept': 'application/json'
                        }
                        response = httpx.post(
                            settings.OCR_SERVICE_URL + '/ocr',
                            files=files,
                            headers=headers,
                            timeout=120
                        )
                    
                    if response.status_code == 200:
                        ocr_result = response.json()
                        logger.info(f"OCR识别结果: {ocr_result}")
                        
                        # 处理新的返回格式
                        if ocr_result.get('result') and len(ocr_result['result']) > 0:
                            # 遍历所有识别结果
                            for text_block in ocr_result['result'][0]:
                                # text_block的格式: [[[x1,y1],[x2,y1],[x2,y2],[x1,y2]], [text, confidence]]
                                text = text_block[1][0]  # 获取识别的文本
                                confidence = text_block[1][1]  # 获取置信度
                                
                                logger.info(f"处理文本: {text} (置信度: {confidence})")
                                
                                # 如果置信度高于阈值（例如0.8）才处理
                                if confidence > 0.8:
                                    mark = extract_foundation_mark(text)
                                    if mark:
                                        logger.info(f"找到基础标记: {mark}")
                                        result['foundation_mark'] = mark
                                        # 保存原始坐标和置信度
                                        result['ocr_info'] = {
                                            'coordinates': text_block[0],
                                            'confidence': confidence
                                        }
                                        break
                    else:
                        logger.error(f"OCR服务返回错误状态码: {response.status_code}")
                        logger.error(f"错误响应: {response.text}")
                        
                except httpx.ConnectError as e:
                    logger.error(f"连接OCR服务失败: {str(e)}")
                except Exception as e:
                    logger.error(f"调用OCR服务时出错: {str(e)}")
                
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                    
            except Exception as e:
                logger.error(f"处理图像时出错: {str(e)}")
                continue
        
        return associated_results
            
    def filter_results(self, results: Dict) -> Dict:
        """过滤和处理识别结果"""
        components = results.get("components", [])
        
        # 生成表格数据
        table_data = []
        logger.info(f"components: {components}, type: {type(components)}")
        for idx, comp in enumerate(components, 1):
            # 使用OCR识别的标记，如果没有则使用默认编号
            mark = comp.get('foundation_mark', f'JC-')
            
            # # 获取OCR信息
            # ocr_info = comp.get('ocr_info', {})
            # confidence = ocr_info.get('confidence', 0)
            
            row = [
                mark,  # 基础标记
                json.dumps({  # 平面信息
                    'jc_ping': comp['jc-ping'],
                    'yolo_ping': comp['ping-yolo']
                }),
                json.dumps({  # 剖面信息
                    'jc_pou': comp['jc-pou'],
                    'yolo_pou': comp['pou-yolo']
                }),
                "-"  # 尺寸（暂时置空）
            ]
            table_data.append(row)
        
        return {
            "status": "success",
            "drawing_id": self.drawing_id,
            "components": components,
            "table": [{
                "title": "基础详图识别结果",
                "columns": ["基础标记", "平面框和YOLO框", "剖面框和YOLO框", "尺寸"],
                "data": table_data
            }],
            "image_path": results.get("image_path", "")
        }