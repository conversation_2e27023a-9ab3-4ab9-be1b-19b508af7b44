from PIL import Image

def split_image(image_path, grid_size=(1024, 1024)):
    """将大图切分为小块，保留不足1024的边缘部分，并记录每个图块的完整坐标."""
    image = Image.open(image_path)
    width, height = image.size
    grid_w, grid_h = grid_size

    images = []
    coordinates = []
    
    for y in range(0, height, grid_h):
        for x in range(0, width, grid_w):
            # 计算切割区域的宽高，确保处理边缘不足 1024 像素的部分
            box_w = min(grid_w, width - x)
            box_h = min(grid_h, height - y)
            
            # 完整的坐标格式： [x_min, y_min, x_max, y_max]
            box = (x, y, x + box_w, y + box_h)
            
            images.append(image.crop(box))
            coordinates.append(box)
    
    return images, coordinates

def merge_results(results, coordinates, original_size):
    merged_results = []
    
    for result, coord in zip(results, coordinates):
        x_offset, y_offset = coord[:2]
        for component in result['results']:
            if len(component['coordinates']) == 4:
                x_min, y_min, x_max, y_max = component['coordinates']
                component['coordinates'] = [
                    x_min + x_offset, y_min + y_offset, 
                    x_max + x_offset, y_max + y_offset
                ]
            merged_results.append(component)
    
    return merged_results

def stitch_images(original_images, result_images, coordinates, original_size):
    stitched_image = Image.new('RGB', original_size)
    
    # 创建一个字典，用坐标作为键来存储结果图像
    result_dict = {coord: img for img, coord in result_images}
    
    for original_img, coord in zip(original_images, coordinates):
        # 如果有对应的结果图像，使用结果图像；否则使用原始图像
        img_to_paste = result_dict.get(coord, original_img)
        stitched_image.paste(img_to_paste, (coord[0], coord[1]))
    
    return stitched_image