import math

def get_scale(coords, actual_length):
    x1, y1, x2, y2 = map(float, coords.split(','))
    pixel_length = math.sqrt((x2 - x1)**2 + (y2 - y1)**2)
    actual_length = float(actual_length)
    scale = actual_length / pixel_length
    return scale

def calculate_beam_length(coordinates, scale):
    x1, y1, x2, y2 = coordinates
    width = abs(x2 - x1)
    height = abs(y2 - y1)
    pixel_length = max(width, height)  # 取长边
    actual_length = pixel_length * scale
    return round(actual_length, 2)  # 四舍五入到小数点后两位


def calculate_column_area(coordinates, scale):
    x1, y1, x2, y2 = coordinates
    width = abs(x2 - x1) * scale
    height = abs(y2 - y1) * scale
    area = width * height
    return round(area, 2)  