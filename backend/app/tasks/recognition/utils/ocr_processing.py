from shapely.geometry import Polygon, MultiPolygon
import re
import logging
import cv2
import numpy as np
import json

logger = logging.getLogger(__name__)


def convert_ocr_coordinates(ocr_results, x_offset, y_offset):
    converted_results = []
    logger.info(f"开始转换OCR结果，输入结果数量: {len(ocr_results)}")
    
    for idx, item in enumerate(ocr_results):
        try:
            logger.debug(f"处理第 {idx+1} 个OCR结果: {item}")
            if isinstance(item, list) and len(item) == 2:
                coords, text_info = item
                if isinstance(coords, list) and len(coords) == 4 and isinstance(text_info, list) and len(text_info) == 2:
                    text, confidence = text_info
                    converted_coords = [[x + x_offset, y + y_offset] for x, y in coords]
                    converted_results.append([converted_coords, [text, confidence]])
                    logger.debug(f"成功转换第 {idx+1} 个OCR结果")
                else:
                    logger.warning(f"第 {idx+1} 个OCR结果内部格式不正确: {item}")
            else:
                logger.warning(f"第 {idx+1} 个OCR结果不是预期的列表格式: {item}")
        except Exception as e:
            logger.error(f"处理第 {idx+1} 个OCR结果时出错: {str(e)}")
    
    logger.info(f"转换前OCR结果数量: {len(ocr_results)}, 转换后数量: {len(converted_results)}")
    return converted_results


def merge_text_boxes(text_boxes, pattern=r'\d+x\d+', merge_distance=20, extract_pattern_only=True):
    """
    合并距离相近的文本框，根据指定模式过滤文本。
    
    Args:
        text_boxes: OCR 结果列表，每个元素包含边界框和文本
        pattern: 正则表达式模式，用于匹配需要的文本格式
        merge_distance: 合并的距离阈值
        extract_pattern_only: 是否只提取匹配模式的部分（True）或保留完整文本（False）
    
    Examples:
        # 匹配尺寸格式 (如 "400x800")
        merge_text_boxes(text_boxes, pattern=r'\d+x\d+')
        
        # 匹配柱标记格式 (如 "KZ-1")
        merge_text_boxes(text_boxes, pattern=r'KZ-\d+', extract_pattern_only=True)
        
        # 匹配梁标记格式 (如 "L-1")
        merge_text_boxes(text_boxes, pattern=r'L-\d+', extract_pattern_only=True)
        
    Returns:
        合并后的文本框列表
    """
    merged_boxes = []
    used = [False] * len(text_boxes)

    logger.info(f"开始合并文本框，共有 {len(text_boxes)} 个文本框")
    logger.info(f"使用匹配模式: {pattern}")
    
    for i, box in enumerate(text_boxes):
        if used[i]:
            continue

        current_poly = Polygon(box[0])
        current_text = box[1][0]
        used[i] = True

        # 检查当前文本是否包含所需格式
        if not re.search(pattern, current_text):
            logger.debug(f"跳过不符合格式的文本: '{current_text}'")
            continue

        logger.info(f"处理第 {i+1} 个文本框，初始文本: '{current_text}'")

        # 合并附近的文本框
        for j, other_box in enumerate(text_boxes[i+1:], start=i+1):
            if used[j]:
                continue

            other_poly = Polygon(other_box[0])
            if current_poly.distance(other_poly) < merge_distance:
                current_poly = current_poly.union(other_poly)
                current_text += ' ' + other_box[1][0]
                used[j] = True
                logger.info(f"  合并第 {j+1} 个文本框，当前文本: '{current_text}'")

        # 处理 MultiPolygon 情况
        if isinstance(current_poly, MultiPolygon):
            logger.info("检测到 MultiPolygon，转换为单一 Polygon")
            minx, miny, maxx, maxy = current_poly.bounds
            current_poly = Polygon([(minx, miny), (maxx, miny), (maxx, maxy), (minx, maxy)])

        # 提取匹配的文本
        matches = re.findall(pattern, current_text)
        if matches:
            if extract_pattern_only:
                # 只保留匹配的部分
                final_text = ', '.join(matches)
            else:
                # 保留完整文本
                final_text = current_text
                
            merged_boxes.append([list(current_poly.exterior.coords), [final_text]])
            logger.info(f"完成第 {i+1} 个文本框的合并，最终文本: '{final_text}'")
        else:
            logger.debug(f"跳过没有匹配内容的文本: '{current_text}'")

    logger.info(f"文本框合并完成，合并后共有 {len(merged_boxes)} 个文本框")
    
    # 打印所有合并后的文本块信息
    logger.info("合并后的所有文本块信息：")
    for idx, merged_box in enumerate(merged_boxes):
        logger.info(f"文本块 {idx+1}: '{merged_box[1][0]}'")

    return merged_boxes


def associate_ocr_with_yolo(yolo_results, ocr_results, distance_threshold=100):
    """将OCR结果与YOLO识别的对象关联."""
    logger.info("开始执行 associate_ocr_with_yolo 函数")
    logger.info(f"接收到的YOLO结果数量: {len(yolo_results)}")
    logger.info(f"接收到的OCR结果数量: {len(ocr_results)}")

    if not ocr_results:
        logger.warning("OCR results are empty or None.")
        return yolo_results  # 或者根据你的需求返回适当的值

    logger.info(f"开始关联 {len(yolo_results)} 个YOLO结果和 {len(ocr_results)} 个OCR结果")

    # 首先合并OCR文本块
    merged_ocr_results = merge_text_boxes(ocr_results)
    logger.info(f"合并后的OCR结果数量: {len(merged_ocr_results)}")
    logger.info(f"合并后的OCR结果: {json.dumps(merged_ocr_results, ensure_ascii=False)}")

    for idx, yolo_obj in enumerate(yolo_results):
        logger.debug(f"正在处理第 {idx+1}/{len(yolo_results)} 个YOLO对象")

        if len(yolo_obj['coordinates']) != 4:
            logger.warning(f"跳过第 {idx+1} 个YOLO对象，坐标格式不正确")
            continue

        yolo_box = np.array([
            [yolo_obj['coordinates'][0], yolo_obj['coordinates'][1]],
            [yolo_obj['coordinates'][2], yolo_obj['coordinates'][1]],
            [yolo_obj['coordinates'][2], yolo_obj['coordinates'][3]],
            [yolo_obj['coordinates'][0], yolo_obj['coordinates'][3]]
        ])

        area_text = []
        min_distance = float('inf')
        closest_ocr = None

        for ocr_idx, ocr_item in enumerate(merged_ocr_results):
            if len(ocr_item[0]) >= 4:
                ocr_poly = Polygon(ocr_item[0])
                ocr_center = np.array(ocr_poly.centroid.coords[0])
                distance = cv2.pointPolygonTest(yolo_box, (ocr_center[0], ocr_center[1]), True)
                logger.debug(f"YOLO对象 {idx+1} 与OCR结果 {ocr_idx+1} 的距离: {distance}")

                if abs(distance) < min_distance:
                    min_distance = abs(distance)
                    closest_ocr = ocr_item
            else:
                logger.warning(f"跳过第 {ocr_idx+1} 个合并后的OCR结果，格式不正确")

        if closest_ocr and min_distance < distance_threshold:
            area_text.append(closest_ocr[1][0])  # OCR识别的文本
            logger.debug(f"第 {idx+1} 个YOLO对象与最近的OCR结果关联，距离: {min_distance:.2f}")

        yolo_obj['area'] = ' '.join(area_text)
        logger.debug(f"第 {idx+1} 个YOLO对象关联了 {len(area_text)} 个OCR结果")

    logger.info(f"所有YOLO结果的关联已完成")
    return yolo_results