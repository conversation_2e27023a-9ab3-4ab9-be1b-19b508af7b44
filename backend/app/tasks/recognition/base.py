from abc import ABC, abstractmethod
from typing import Dict, Optional
from pathlib import Path
import logging
from app.models import Drawing
import math


logger = logging.getLogger(__name__)

class RecognitionStrategy(ABC):
    """识别策略基类"""
    
    def __init__(self, drawing_id: int, file_path: str, drawing: Drawing):
        self.drawing_id = drawing_id
        self.file_path = Path('/app') / file_path
        self.drawing = drawing
        self.scale: Optional[float] = None
        self.yolov9_dir = Path('/app/yolov9')
        
    @property
    def needs_scale(self) -> bool:
        """是否需要比例尺"""
        return False
        
    def validate_and_set_scale(self) -> None:
        """验证并设置比例尺"""
        if not self.needs_scale:
            return
            
        if not self.drawing.grid_data:
            raise ValueError(f"图纸 {self.drawing_id} 缺少轴网数据，请先使用标定接口上传轴网信息")
            
        try:
            coords = self.drawing.grid_data['coordinates'][0]
            actual_length = float(self.drawing.grid_data['length'])
            
            x1, y1, x2, y2 = map(float, coords.split(','))
            pixel_length = math.sqrt((x2 - x1)**2 + (y2 - y1)**2)
            
            self.scale = actual_length / pixel_length
            logger.info(f"计算得到比例尺: {self.scale}")
            
        except (KeyError, ValueError, TypeError, IndexError) as e:
            logger.error(f"计算比例尺失败: {str(e)}")
            raise ValueError("轴网数据格式错误，请检查标定数据")
    
    def execute(self) -> Dict:
        """执行完整流程"""
        try:
            # 如果策略需要比例尺，先验证并设置
            self.validate_and_set_scale()
            
            # 执行识别流程
            self.preprocess()
            results = self.perform_recognition()
            filtered_results = self.filter_results(results)
            return filtered_results
            
        except Exception as e:
            logger.error(f"Recognition failed: {str(e)}")
            raise