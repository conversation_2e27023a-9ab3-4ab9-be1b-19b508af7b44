from typing import Dict, List
import logging
import os
import json
import subprocess
from PIL import Image
from pathlib import Path
import httpx

from app.core.config import settings, yolo_class_mapping
from app.tasks.recognition.base import RecognitionStrategy
from app.tasks.recognition.utils.image_processing import split_image, merge_results, stitch_images
from app.tasks.recognition.utils.ocr_processing import convert_ocr_coordinates, associate_ocr_with_yolo
from app.tasks.recognition.utils.geometry import calculate_beam_length


logger = logging.getLogger(__name__)

class BeamRecognitionStrategy(RecognitionStrategy):
    """梁识别策略"""
    
    @property
    def needs_scale(self) -> bool:
        return True
        
    def preprocess(self) -> None:
        """预处理步骤，验证必要数据"""
        if self.scale is None:
            raise ValueError("未设置比例尺，无法进行梁识别")
            
    def perform_recognition(self) -> Dict:
        """执行识别流程"""
        logger.info(f"开始识别梁构件，图纸ID: {self.drawing_id}")
        try:
            # 检查文件是否存在
            if not Path(self.file_path).exists():
                raise FileNotFoundError(f"File not found: {self.file_path}")
                
            # 切换到 YOLOv9 目录
            os.chdir(self.yolov9_dir)
            logger.info(f"Current working directory: {os.getcwd()}")
            
            # 切分图像
            logger.info("正在切分图纸...")
            original_images, coordinates = split_image(self.file_path)
            logger.info(f"图纸已被切分为 {len(original_images)} 个部分")
            
            # 创建保存目录
            save_dir = self.yolov9_dir / f"split_img/{self.drawing_id}"
            os.makedirs(save_dir, exist_ok=True)
            
            all_yolo_results = []
            all_ocr_results = []
            result_images = []
            
            # 处理每个切片
            for idx, (image, coord) in enumerate(zip(original_images, coordinates)):
                logger.info(f"正在处理第 {idx+1}/{len(original_images)} 个切分图像")
                
                # 保存切片图像
                split_image_path = save_dir / f"split_image_{idx}.jpg"
                image.save(split_image_path)
                
                # OCR处理
                logger.info(f"正在发送第 {idx+1} 个切分图像到OCR服务")
                with open(split_image_path, "rb") as image_file:
                    files = {"file": ("image.jpg", image_file, "image/jpeg")}
                    response = httpx.post(f"{settings.OCR_SERVICE_URL}/ocr", 
                                        files=files, timeout=120)
                    
                if response.status_code != 200:
                    raise Exception(f"OCR服务返回状态码 {response.status_code}")
                    
                ocr_output = response.json()
                logger.info(f"已接收第 {idx+1} 个切分图像的OCR结果")
                
                # 处理OCR结果
                if ocr_output['result'] and not all(item is None for item in ocr_output['result']):
                    converted_ocr_results = convert_ocr_coordinates(
                        ocr_output['result'][0], coord[0], coord[1]
                    )
                    all_ocr_results.extend(converted_ocr_results)
                    
                # YOLO检测
                logger.info(f"正在执行第 {idx+1} 个切分图像的YOLO检测")
                if self.drawing.discipline == "建筑工程" and self.drawing.category == "梁":
                    model_path = settings.YOLO_MODELS["建筑工程_其他"]
                else:
                    model_path = settings.YOLO_MODELS[self.drawing.discipline]
                    
                command = f"python detect.py --source {split_image_path} --img 1024 --weights '{model_path}' --device 0 --name yolov9_c_c_640_val --json"
                result = subprocess.run(command, shell=True, check=True, 
                                     capture_output=True, text=True)
                yolo_output = json.loads(result.stdout)
                
                # 合并YOLO结果
                all_yolo_results.extend(
                    merge_results([yolo_output], [coord], image.size)
                )
                
                # 保存结果图像
                result_image_path = yolo_output['image_path']
                if os.path.exists(result_image_path):
                    result_images.append((Image.open(result_image_path), coord))
                else:
                    result_images.append((image, coord))
                    
            # 拼接结果图像
            original_size = Image.open(self.file_path).size
            stitched_image = stitch_images(original_images, result_images, 
                                         coordinates, original_size)
            
            # 保存拼接图像
            save_dir = self.yolov9_dir / 'stitched_image' / str(self.drawing_id)
            os.makedirs(save_dir, exist_ok=True)
            stitched_image_path = save_dir / 'stitched_result.jpg'
            stitched_image.save(stitched_image_path)
            
            # 关联YOLO结果和OCR结果
            logger.info("开始关联YOLO结果和OCR结果")
            components = associate_ocr_with_yolo(all_yolo_results, all_ocr_results)
            
            # 修改返回的图片URL路径
            relative_path = f"{self.drawing_id}/stitched_result.jpg"
            image_url = f"http://{settings.DOMAIN}:8091/static/result/{relative_path}"
            
            return {
                "components": components,
                "image_path": image_url
            }

        except Exception as e:
            logger.error(f"梁识别过程出错: {str(e)}")
            raise
        
    def filter_results(self, results: Dict) -> Dict:
        """过滤和处理识别结果"""
        logger.info(f"开始过滤识别结果: {json.dumps(results, ensure_ascii=False)}")
        
        # 根据 discipline 和 category 确定使用的映射
        if self.drawing.discipline == "建筑工程" and self.drawing.category == "梁":
            model_key = "建筑工程_其他"
        else:
            raise ValueError(f"Unsupported discipline: {self.drawing.discipline}")
        yolo_mapping = yolo_class_mapping.get(model_key, {})
        logger.info(f"使用模型映射: {model_key}")
        
        # 获取所有梁构件
        filtered_components = []
        for comp in results.get("components", []):
            try:
                class_id = str(comp.get("class_id"))
                # 获取映射的类名
                class_info = yolo_mapping.get(class_id, {})
                class_name = class_info.get('Class_Name', '')
                
                # 检查是否为梁构件（通过class_id为7或class_name包含"梁"）
                if class_name == "梁":
                    logger.info(f"找到梁构件: {json.dumps(comp, ensure_ascii=False)}")
                    
                    # 计算实际长度
                    try:
                        coordinates = comp["coordinates"]  # 这应该是 [x1, y1, x2, y2] 格式
                        length = self._calculate_beam_length(coordinates)
                        comp["length"] = length
                        filtered_components.append(comp)
                    except Exception as e:
                        logger.error(f"计算梁长度时出错: {str(e)}")
                        continue
                                
            except Exception as e:
                logger.error(f"处理构件时出错: {str(e)}")
                continue   
        
        logger.info(f"过滤后的梁构件数量: {len(filtered_components)}")
        
        # 生成表格数据
        table_data = []
        for idx, comp in enumerate(filtered_components):
            try:
                # 保持原始坐标格式 [x1, y1, x2, y2]
                coords = comp['coordinates']
                row = [
                    "梁",  # 构件类型
                    str(coords),  # 保持完整的坐标格式
                    f"{comp['length']:.2f}",  # 长度
                    comp.get('area', '-'),  # OCR识别的尺寸信息
                    "-",  # 体积
                    str(len(filtered_components)) if idx == 0 else "-"  # 数量
                ]
                table_data.append(row)
            except Exception as e:
                logger.error(f"生成表格数据时出错: {str(e)}")
                continue
        
        return {
            "status": "success",
            "drawing_id": self.drawing_id,
            "components": filtered_components,  # 这里包含完整的组件信息，包括原始坐标
            "table": [{
                "title": "识别结果",
                "columns": ["构件类型", "位置坐标", "长度", "面积", "体积", "数量"],
                "data": table_data
            }],
            "image_path": results.get("image_path", "")
        }
            
        
    def _calculate_beam_length(self, coordinates: List[float]) -> float:
        """计算梁的实际长度"""
        try:
            x1, y1, x2, y2 = coordinates
            width = abs(x2 - x1)
            height = abs(y2 - y1)
            pixel_length = max(width, height)  # 取长边作为梁的长度
            actual_length = pixel_length * self.scale
            return round(actual_length, 2)
        except Exception as e:
            logger.error(f"计算梁长度时出错: {str(e)}")
            raise ValueError(f"计算梁长度失败: {str(e)}")