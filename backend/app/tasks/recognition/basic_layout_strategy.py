from PIL import Image
from typing import Dict, List
import math
import uuid
import re
import os
import json
import httpx
import logging

from .base import RecognitionStrategy
from app.models import Drawing
from app.core.config import settings

logger = logging.getLogger(__name__)

class BasicLayoutRecognitionStrategy(RecognitionStrategy):
    """基础布置图识别策略"""
    
    def __init__(self, drawing_id: int, file_path: str, drawing: Drawing):
        super().__init__(drawing_id, file_path, drawing)
        self.tile_size = 900
        
    def preprocess(self) -> None:
        """
        预处理方法，基础布置图不需要特殊预处理
        """
        logger.info(f"基础布置图 {self.drawing_id} 开始预处理...")
        # 基础布置图不需要特殊预处理
        pass
        
    def postprocess(self) -> None:
        """
        后处理方法，基础布置图不需要特殊后处理
        """
        logger.info(f"基础布置图 {self.drawing_id} 开始后处理...")
        # 基础布置图不需要特殊后处理
        pass
    
    def filter_results(self, results: Dict) -> Dict:
        """过滤识别结果，基础布置图不需要过滤"""
        logger.info(f"基础布置图 {self.drawing_id} 过滤识别结果...")
        return results
        
    def split_image(self, image: Image.Image) -> List[Dict]:
        """
        将图像切分为900x900的小块
        
        Args:
            image: PIL Image对象
        
        Returns:
            List[Dict]: 包含切分图像和对应坐标信息的列表
        """
        width, height = image.size
        tiles = []
        
        # 计算需要切分的行数和列数
        cols = math.ceil(width / self.tile_size)
        rows = math.ceil(height / self.tile_size)
        
        for row in range(rows):
            for col in range(cols):
                # 计算当前切片的坐标
                x1 = col * self.tile_size
                y1 = row * self.tile_size
                x2 = min((col + 1) * self.tile_size, width)
                y2 = min((row + 1) * self.tile_size, height)
                
                # 切分图像
                tile = image.crop((x1, y1, x2, y2))
                
                # 保存切片信息
                tiles.append({
                    'image': tile,
                    'coordinates': {
                        'x1': x1,
                        'y1': y1,
                        'x2': x2,
                        'y2': y2
                    }
                })
        
        return tiles
    
    def adjust_coordinates(self, box: List[float], tile_coords: Dict) -> List[int]:
        """
        调整OCR识别到的坐标到原图坐标系
        
        Args:
            box: OCR识别的坐标 [x1,y1,x2,y2]
            tile_coords: 切片的坐标信息
            
        Returns:
            List[int]: 调整后的坐标
        """
        return [
            int(box[0] + tile_coords['x1']),  # x1
            int(box[1] + tile_coords['y1']),  # y1
            int(box[2] + tile_coords['x1']),  # x2
            int(box[3] + tile_coords['y1'])   # y2
        ]
        
    def perform_recognition(self) -> Dict:
        """执行基础布置图识别"""
        try:
            logger.info("开始基础布置图识别...")
            
            # 读取图像
            image = Image.open(self.file_path)
            
            # 切分图像
            tiles = self.split_image(image)
            
            # 用于存储所有识别结果
            symbol_dict = {}
            
            # 处理每个切片
            for tile_info in tiles:
                # 保存临时文件
                temp_path = f"temp_{uuid.uuid4()}.jpg"
                tile_info['image'].save(temp_path)
                
                try:
                    # 发送OCR请求
                    with open(temp_path, "rb") as image_file:
                        files = {
                            "file": ("image.jpg", image_file, "image/jpeg")
                        }
                        headers = {'accept': 'application/json'}
                        response = httpx.post(
                            settings.OCR_SERVICE_URL + '/ocr',
                            files=files,
                            headers=headers,
                            timeout=120
                        )
                    
                    if response.status_code != 200:
                        logger.error(f"OCR服务返回错误状态码: {response.status_code}")
                        continue
                        
                    ocr_result = response.json()
                    
                    # 处理OCR结果
                    if ocr_result.get('result') and len(ocr_result['result']) > 0:
                        for text_block in ocr_result['result'][0]:
                            coords = text_block[0]
                            text = text_block[1][0]
                            confidence = text_block[1][1]
                            
                            # 匹配 xx-数字 格式
                            match = re.match(r'([A-Za-z]+)-(\d+)', text)
                            if match and confidence > 0.8:
                                symbol = text.strip()
                                
                                # 转换坐标格式并调整到原图坐标系
                                box = [
                                    coords[0][0],  # x1
                                    coords[0][1],  # y1
                                    coords[2][0],  # x2
                                    coords[2][1]   # y2
                                ]
                                adjusted_box = self.adjust_coordinates(box, tile_info['coordinates'])
                                
                                # 将相同符号的坐标添加到字典中
                                if symbol not in symbol_dict:
                                    symbol_dict[symbol] = {
                                        'coordinates': [adjusted_box],
                                        'count': 1
                                    }
                                else:
                                    symbol_dict[symbol]['coordinates'].append(adjusted_box)
                                    symbol_dict[symbol]['count'] += 1
                    
                finally:
                    # 清理临时文件
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
            
            # 生成返回结果
            table_data = []
            for symbol, info in symbol_dict.items():
                row = [
                    symbol,  # 基础符号
                    json.dumps(info['coordinates']),  # 坐标列表
                    f"{info['count']}个"  # 数量
                ]
                table_data.append(row)
                
            logger.info(f"基础布置图识别结果: {table_data}")

            return {
                "status": "success",
                "drawing_id": self.drawing_id,
                "table": [{
                    "title": "基础布置图识别结果",
                    "columns": ["基础符号", "坐标", "数量"],
                    "data": table_data
                }]
            }
            
        except Exception as e:
            logger.error(f"基础布置图识别过程出错: {str(e)}")
            raise