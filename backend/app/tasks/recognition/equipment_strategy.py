import logging
import os
import json
import subprocess
from pathlib import Path
from PIL import Image
from typing import Dict, List
import httpx

from app.core.config import settings, yolo_class_mapping
from app.tasks.recognition.base import RecognitionStrategy
from app.tasks.recognition.utils.image_processing import split_image, merge_results, stitch_images

logger = logging.getLogger(__name__)

class EquipmentRecognitionStrategy(RecognitionStrategy):
    """设备识别策略"""
    
    @property
    def needs_scale(self) -> bool:
        return False  # 设备识别不需要比例尺
        
    def preprocess(self) -> None:
        """预处理步骤，设备识别不需要特殊预处理"""
        pass
        
    def perform_recognition(self) -> Dict:
        """执行识别流程"""
        logger.info(f"开始识别设备，图纸ID: {self.drawing_id}")
        try:
            # 检查文件是否存在
            if not Path(self.file_path).exists():
                raise FileNotFoundError(f"File not found: {self.file_path}")
                
            # 切换到 YOLOv9 目录
            os.chdir(self.yolov9_dir)
            logger.info(f"Current working directory: {os.getcwd()}")
            
            # 切分图像
            logger.info("正在切分图纸...")
            original_images, coordinates = split_image(self.file_path)
            logger.info(f"图纸已被切分为 {len(original_images)} 个部分")
            
            # 创建保存目录
            save_dir = self.yolov9_dir / f"split_img/{self.drawing_id}"
            os.makedirs(save_dir, exist_ok=True)
            
            all_yolo_results = []
            result_images = []
            
            # 处理每个切片
            for idx, (image, coord) in enumerate(zip(original_images, coordinates)):
                logger.info(f"正在处理第 {idx+1}/{len(original_images)} 个切分图像")
                
                # 保存切片图像
                split_image_path = save_dir / f"split_image_{idx}.jpg"
                image.save(split_image_path)
                
                # YOLO检测
                logger.info(f"正在执行第 {idx+1} 个切分图像的YOLO检测")
                model_path = settings.YOLO_MODELS["设备安装_设备"]
                
                command = f"python detect.py --source {split_image_path} --img 1024 --weights '{model_path}' --device 0 --name yolov9_c_c_640_val --json"
                result = subprocess.run(command, shell=True, check=True, 
                                     capture_output=True, text=True)
                yolo_output = json.loads(result.stdout)
                # 在 YOLO 检测后添加日志
                logger.info(f"YOLO 原始输出: {json.dumps(yolo_output, ensure_ascii=False)}")

                # 合并YOLO结果
                all_yolo_results.extend(
                    merge_results([yolo_output], [coord], image.size)
                )
                
                # 保存结果图像
                result_image_path = yolo_output['image_path']
                if os.path.exists(result_image_path):
                    result_images.append((Image.open(result_image_path), coord))
                else:
                    result_images.append((image, coord))
                    
            # 拼接结果图像
            original_size = Image.open(self.file_path).size
            stitched_image = stitch_images(original_images, result_images, 
                                         coordinates, original_size)
            
            # 保存拼接图像
            save_dir = self.yolov9_dir / 'stitched_image' / str(self.drawing_id)
            os.makedirs(save_dir, exist_ok=True)
            stitched_image_path = save_dir / 'stitched_result.jpg'
            stitched_image.save(stitched_image_path)
            
            # 修改返回的图片URL路径
            relative_path = f"{self.drawing_id}/stitched_result.jpg"
            image_url = f"http://{settings.DOMAIN}:8091/static/result/{relative_path}"
            
            return {
                "components": all_yolo_results,
                "image_path": image_url
            }

        except Exception as e:
            logger.error(f"设备识别过程出错: {str(e)}")
            raise
        
    def filter_results(self, results: Dict) -> Dict:
        """过滤和处理识别结果"""
        logger.info("开始处理识别结果")
        
        # 根据 discipline 确定使用的映射
        model_key = "设备安装_设备"  # 假设设备安装的 model_key 是固定的
        yolo_mapping = yolo_class_mapping.get(model_key, {})
        logger.info(f"使用模型映射: {model_key}")
        
        # 获取所有构件
        filtered_components = []
        equipment_count = {}  # 用于统计每种设备的数量
        
        for comp in results.get("components", []):
            try:
                class_id = str(comp.get("class_id"))
                # 获取映射的类名
                class_info = yolo_mapping.get(class_id, {})
                chinese_name = class_info.get('Class_Name', '未知设备')
                
                logger.info(f"找到设备构件: {json.dumps(comp, ensure_ascii=False)}")
                
                # 记录设备信息
                equipment_count[chinese_name] = equipment_count.get(chinese_name, 0) + 1
                
                # 添加中文名称到组件信息中
                comp["chinese_name"] = chinese_name
                filtered_components.append(comp)
                    
            except Exception as e:
                logger.error(f"处理构件时出错: {str(e)}")
                continue
                
        logger.info(f"识别到的设备构件数量: {len(filtered_components)}")
        
        # 生成表格数据
        table_data = []
        for equipment_name, count in equipment_count.items():
            # 找到该类型设备的所有坐标
            coordinates = [
                comp['coordinates'] 
                for comp in filtered_components 
                if comp['chinese_name'] == equipment_name
            ]
            
            row = [
                equipment_name,  # 设备中文名称
                str(coordinates),  # 坐标列表
                str(count)  # 数量
            ]
            table_data.append(row)
            
        return {
            "status": "success",
            "drawing_id": self.drawing_id,
            "components": filtered_components,  # 包含完整的组件信息，包括原始坐标和中文名称
            "table": [{
                "title": "识别结果",
                "columns": ["设备名称", "位置坐标", "数量"],
                "data": table_data
            }],
            "image_path": results.get("image_path", "")
        }