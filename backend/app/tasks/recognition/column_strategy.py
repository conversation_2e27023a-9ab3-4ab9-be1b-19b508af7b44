import logging
from typing import Dict
from PIL import Image
import os
import json
import subprocess
from pathlib import Path
import httpx
import numpy as np
from shapely.geometry import Polygon
import tempfile
from app.core.config import settings
from app.tasks.recognition.base import RecognitionStrategy
from app.tasks.recognition.utils.image_processing import split_image, merge_results, stitch_images
from app.tasks.recognition.utils.ocr_processing import convert_ocr_coordinates, associate_ocr_with_yolo, merge_text_boxes
from app.tasks.recognition.utils.geometry import calculate_beam_length

logger = logging.getLogger(__name__)

class ColumnRecognitionStrategy(RecognitionStrategy):
    """柱识别策略"""
    
    @property
    def needs_scale(self) -> bool:
        return False
        
    def preprocess(self) -> None:
        """预处理步骤，验证必要数据"""
        if not self.drawing.grid_data:
            raise ValueError("缺少网格数据")
        
        grid_data = self.drawing.grid_data
        if "zhubiao" not in grid_data or "zhutu" not in grid_data:
            raise ValueError("缺少柱表或柱图区域数据")
            
    def perform_recognition(self) -> Dict:
        """执行识别流程"""
        logger.info(f"开始识别柱构件，图纸ID: {self.drawing_id}")
        try:
            # 1. 获取柱表区域
            grid_data = self.drawing.grid_data
            zhubiao_coords = json.loads(grid_data["zhubiao"]) if isinstance(grid_data["zhubiao"], str) else grid_data["zhubiao"]
            zhutu_coords = json.loads(grid_data["zhutu"]) if isinstance(grid_data["zhutu"], str) else grid_data["zhutu"]
            
            logger.info(f"柱表区域坐标: {zhubiao_coords}")
            logger.info(f"柱图区域坐标: {zhutu_coords}")
            
            # 2. 处理柱表区域
            # 打开原始图片
            with Image.open(self.file_path) as img:
                # 裁剪柱表区域
                x1, y1, x2, y2 = map(int, zhubiao_coords)
                zhubiao_img = img.crop((x1, y1, x2, y2))
                
                # 保存临时文件
                with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
                    zhubiao_img.save(tmp_file.name)
                    
                    # 发送到表格识别服务
                    logger.info("发送柱表区域到表格识别服务")
                    with open(tmp_file.name, 'rb') as f:
                        files = {"file": ("zhubiao.jpg", f, "image/jpeg")}
                        response = httpx.post(
                            f"{settings.OCR_SERVICE_URL}/extract-table",
                            files=files,
                            timeout=120
                        )
                    
                    if response.status_code != 200:
                        raise Exception(f"表格识别服务返回错误: {response.status_code}")
                        
                    zhubiao_data = response.json()
                    logger.info("成功获取柱表数据")
                    
                # 3. 处理柱图区域
                # 裁剪柱图区域
                x1, y1, x2, y2 = map(int, zhutu_coords)
                zhutu_img = img.crop((x1, y1, x2, y2))
                
                # 保存临时文件
                with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
                    zhutu_img.save(tmp_file.name)
                    
                    # 发送到OCR服务识别柱号
                    logger.info("发送柱图区域到OCR服务")
                    with open(tmp_file.name, 'rb') as f:
                        files = {"file": ("zhutu.jpg", f, "image/jpeg")}
                        response = httpx.post(
                            f"{settings.OCR_SERVICE_URL}/ocr",
                            files=files,
                            timeout=120
                        )
                    
                    if response.status_code != 200:
                        raise Exception(f"OCR服务返回错误: {response.status_code}")
                        
                    ocr_results = response.json()
                    logger.info("成功获取OCR结果")
            
            return {
                "zhubiao_data": zhubiao_data["result"],
                "ocr_results": ocr_results["result"],
                "zhubiao_coords": zhubiao_coords,
                "zhutu_coords": zhutu_coords
            }
            
        except Exception as e:
            logger.error(f"柱识别过程出错: {str(e)}")
            raise
            
    def filter_results(self, results: Dict) -> Dict:
        """过滤和处理识别结果"""
        logger.info("开始处理识别结果")
        
        # 1. 处理柱表数据作为第一个表格
        zhubiao_table = []
        for kz_number, details in results["zhubiao_data"].items():
            for detail in details:
                row = [
                    kz_number,                    # 柱号
                    detail.get("标高", "-"),      # 标高
                    detail.get("bxh", "-"),       # 截面尺寸
                    detail.get("箍筋", "-"),      # 箍筋
                    detail.get("全部纵筋", "-")   # 纵筋
                ]
                zhubiao_table.append(row)
                
        # 2. 统计OCR识别到的柱号
        column_stats = {}
        for text_block in results["ocr_results"][0]:  # 假设OCR结果在第一个列表中
            text = text_block.get("text", "").strip()
            if text.startswith("KZ"):  # 假设柱号都是KZ开头
                coords = text_block.get("box", [])  # 获取文字框坐标
                # 调整坐标（加上zhutu区域的偏移）
                adjusted_coords = [
                    coords[0] + results["zhutu_coords"][0],
                    coords[1] + results["zhutu_coords"][1],
                    coords[2] + results["zhutu_coords"][0],
                    coords[3] + results["zhutu_coords"][1]
                ]
                if text in column_stats:
                    column_stats[text]["count"] += 1
                    column_stats[text]["coords"].append(adjusted_coords)
                else:
                    column_stats[text] = {
                        "count": 1,
                        "coords": [adjusted_coords]
                    }
                    
        # 生成柱统计表格
        column_count_table = [
            [column_id, str(stats["coords"]), str(stats["count"])]
            for column_id, stats in column_stats.items()
        ]
        
        return {
            "status": "success",
            "drawing_id": self.drawing_id,
            "table": [
                {
                    "title": "柱表信息",
                    "columns": ["柱号", "标高", "截面尺寸", "箍筋", "纵筋"],
                    "data": zhubiao_table
                },
                {
                    "title": "柱统计信息",
                    "columns": ["柱编号", "坐标", "数量"],
                    "data": column_count_table
                }
            ]
        }