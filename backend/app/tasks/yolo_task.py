import os
import json
import subprocess
import logging
from pathlib import Path
import mimetypes
import httpx
import cv2
import numpy as np
import re
import math
import traceback

from collections import defaultdict
from sqlmodel import select
from datetime import datetime
from fastapi import Depends
from sqlmodel import Session
from typing import Annotated
from sqlalchemy.exc import SQLAlchemyError
from contextlib import contextmanager
from PIL import Image
from shapely.geometry import box, Polygon, MultiPolygon

from app.celery_init import celery_app
from app.core.db import celery_engine
from app.core.config import settings, yolo_class_mapping
from app.models.drawing import Drawing
from app.models.recognition_tasks import RecognitionTask
from app.tasks.strategy_factory import RecognitionStrategyFactory
from app.tasks.db_session import get_celery_db_session


OCR_SERVICE_URL = os.getenv("OCR_SERVICE_URL", "http://ocr_service:8000")
PADDLE_OCR_URL = os.getenv("PADDLE_OCR_URL", "http://paddle_ocr:8000")

logger = logging.getLogger(__name__) 

@celery_app.task(name="app.tasks.recognition_tasks.recognize_drawing_components")
def recognize_drawing_components(file_path: str, drawing_id: int, drawing_type: str):
    """识别图纸组件"""
    logger.info(f"开始识别图纸组件，图纸ID: {drawing_id}, 类型: {drawing_type}")
    try:
        # 获取图纸信息
        with get_celery_db_session() as db:
            drawing = db.get(Drawing, drawing_id)
            if not drawing:
                raise ValueError(f"Drawing with ID {drawing_id} not found")
        
        # 获取对应的识别策略
        strategy = RecognitionStrategyFactory.get_strategy(
            drawing_type,
            drawing_id=drawing_id,
            file_path=file_path,
            drawing=drawing  # 传入整个 drawing 对象
        )
        
        # 执行识别
        result = strategy.execute()
        
        # 更新数据库
        with get_celery_db_session() as db:
            drawing = db.get(Drawing, drawing_id)
            if drawing:
                drawing.origin_result = result
                drawing.finally_result = result
                
            task = db.exec(
                select(RecognitionTask)
                .where(RecognitionTask.drawing_id == drawing_id)
                .where(RecognitionTask.celery_task_id == recognize_drawing_components.request.id)
            ).first()
            
            if task:
                task.result = result
                task.status = "COMPLETED"
                task.completed_at = datetime.utcnow()
                
            db.commit()
            
        return result
    
    except Exception as e:
        # 获取完整的堆栈跟踪
        error_traceback = traceback.format_exc()
        
        # 记录详细错误信息
        logger.error(f"图纸组件识别出错，图纸ID {drawing_id}:")
        logger.error(f"错误类型: {type(e).__name__}")
        logger.error(f"错误信息: {str(e)}")
        logger.error(f"堆栈跟踪:\n{error_traceback}")
        
        error_result = {
            "status": "error",
            "error_message": str(e),
            "error_type": type(e).__name__,
            "error_traceback": error_traceback,
            "drawing_id": drawing_id,
            "error_time": datetime.utcnow().isoformat()
        }
        
    # except Exception as e:
    #     logger.error(f"图纸组件识别出错，图纸ID {drawing_id}: {str(e)}")
    #     error_result = {
    #         "status": "error",
    #         "error_message": str(e),
    #         "drawing_id": drawing_id,
    #         "error_time": datetime.utcnow().isoformat()
    #     }
        
        # 出错时也更新数据库
        with get_celery_db_session() as db:
            drawing = db.get(Drawing, drawing_id)
            if drawing:
                drawing.origin_result = error_result
                drawing.finally_result = error_result
                
            task = db.exec(
                select(RecognitionTask)
                .where(RecognitionTask.drawing_id == drawing_id)
                .where(RecognitionTask.celery_task_id == recognize_drawing_components.request.id)
            ).first()
            
            if task:
                task.result = error_result
                task.status = "FAILED"
                task.completed_at = datetime.utcnow()
                
            db.commit()
            
        return error_result
