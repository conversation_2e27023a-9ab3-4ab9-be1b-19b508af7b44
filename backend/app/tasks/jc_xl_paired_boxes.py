import cv2
import numpy as np
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

def extract_subimages_with_titles(image_path, min_size=(20, 20), title_padding=200, 
                                crop_margin_x=300, crop_margin_y=300):
    """
    从CAD图像中提取带标题的子图，并过滤掉小尺寸的图像
    """
    # 读取图像
    image = cv2.imread(str(image_path))
    if image is None:
        raise ValueError("无法读取图像")
    
    # 四边裁剪
    h, w = image.shape[:2]
    x1, y1 = crop_margin_x, crop_margin_y
    x2, y2 = w - crop_margin_x, h - crop_margin_y

    # 确保裁剪区域有效
    if x2 > x1 and y2 > y1:
        image = image[y1:y2, x1:x2]
    else:
        raise ValueError("裁剪后图像尺寸无效，请检查边界裁剪大小是否过大")
    
    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 使用自适应阈值进行二值化
    binary = cv2.adaptiveThreshold(
        gray,
        255,
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY_INV,
        11,
        2
    )
    
    # 分别处理图形内容和文字
    kernel_content = np.ones((3, 3), np.uint8)
    binary_content = cv2.dilate(binary, kernel_content, iterations=2)
    
    kernel_text = np.ones((2, 5), np.uint8)
    binary_text = cv2.dilate(binary, kernel_text, iterations=1)
    
    # 合并图形和文字的二值图
    binary_combined = cv2.bitwise_or(binary_content, binary_text)
    
    # 寻找轮廓
    contours, _ = cv2.findContours(binary_combined, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # 存储检测到的子图信息
    subimages = []
    
    for contour in contours:
        # 获取边界框
        x, y, w, h = cv2.boundingRect(contour)
        
        # 检查尺寸是否满足最小要求
        if w < min_size[0] or h < min_size[1]:
            continue
        
        # 检查下方是否有文字
        title_region = binary[y+h:y+h+title_padding, x:x+w]
        if np.sum(title_region) > 0:
            text_positions = np.where(title_region > 0)[0]
            if len(text_positions) > 0:
                h += min(title_padding, np.max(text_positions) + 10)
        
        # 增加边距
        padding = 10
        x1_box = max(0, x - padding)
        y1_box = max(0, y - padding)
        x2_box = min(image.shape[1], x + w + padding)
        y2_box = min(image.shape[0], y + h + padding)
        
        # 最终检查裁剪后的尺寸
        final_width = x2_box - x1_box
        final_height = y2_box - y1_box
        
        if final_width < min_size[0] or final_height < min_size[1]:
            continue
        
        # 保存子图信息，坐标需要加上裁剪偏移
        subimages.append({
            'bbox': [
                x1_box + crop_margin_x,
                y1_box + crop_margin_y,
                x2_box + crop_margin_x,
                y2_box + crop_margin_y
            ]
        })
    
    # 按照从左到右，从上到下的顺序排序子图
    subimages.sort(key=lambda x: (x['bbox'][1] // 100, x['bbox'][0]))
    
    return subimages

def pair_subimages(subimages, max_vertical_distance=300):
    """
    将子图按照垂直位置配对，遵循以下规则：
    1. 按照垂直位置（y坐标）排序所有子图
    2. 寻找最近的垂直距离合适的配对
    3. 确保配对的图像在合理的垂直距离范围内
    """
    # 按y坐标排序
    sorted_subimages = sorted(enumerate(subimages), key=lambda x: (x[1]['bbox'][1]))
    used_indices = set()
    pairs = []
    
    # 遍历每个子图
    for i, (idx1, img1) in enumerate(sorted_subimages):
        if idx1 in used_indices:
            continue
            
        # 获取当前图像的边界框
        box1 = img1['bbox']
        min_distance = float('inf')
        best_match = None
        
        # 寻找最近的下方图像
        for j, (idx2, img2) in enumerate(sorted_subimages[i+1:], i+1):
            if idx2 in used_indices:
                continue
                
            box2 = img2['bbox']
            # 计算两个框的垂直距离（从上框底部到下框顶部）
            vertical_distance = box2[1] - box1[3]  # y3 - y2
            
            # 检查垂直距离是否在合理范围内
            if 0 <= vertical_distance <= max_vertical_distance:
                # 检查水平重叠
                x_overlap = not (box2[2] < box1[0] or box2[0] > box1[2])
                
                if x_overlap and vertical_distance < min_distance:
                    min_distance = vertical_distance
                    best_match = idx2
        
        # 如果找到合适的匹配，记录配对
        if best_match is not None:
            pairs.append((idx1, best_match))
            used_indices.add(idx1)
            used_indices.add(best_match)
    
    return pairs

def extract_paired_boxes(image_path, min_size=(100, 100), title_padding=100, 
                        crop_margin_x=350, crop_margin_y=250, max_vertical_distance=300):
    """主函数：提取并配对框"""
    # 提取子图
    subimages = extract_subimages_with_titles(
        image_path,
        min_size=min_size,
        title_padding=title_padding,
        crop_margin_x=crop_margin_x,
        crop_margin_y=crop_margin_y
    )
    
    # 配对子图
    pairs = pair_subimages(subimages, max_vertical_distance)
    
    # 转换为所需的返回格式
    result = []
    for idx1, idx2 in pairs:
        result.append({
            'jc-ping': [
                int(subimages[idx1]['bbox'][0]),
                int(subimages[idx1]['bbox'][1]),
                int(subimages[idx1]['bbox'][2]),
                int(subimages[idx1]['bbox'][3])
            ],
            'jc-pou': [
                int(subimages[idx2]['bbox'][0]),
                int(subimages[idx2]['bbox'][1]),
                int(subimages[idx2]['bbox'][2]),
                int(subimages[idx2]['bbox'][3])
            ]
        })
    
    return result

def main():
    # 使用示例
    image_path = "xiangtu.png"
    try:
        result = extract_paired_boxes(image_path)
        print(f"成功找到 {len(result)} 对配对的子图")
        for i, pair in enumerate(result):
            print(f"配对 {i+1}:")
            print(f"  平面图框: {pair['jc-ping']}")
            print(f"  剖面图框: {pair['jc-pou']}")
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")

if __name__ == "__main__":
    main()