import numpy as np
from PIL import Image
import os
import re
import logging
import requests
from typing import List, Dict, <PERSON><PERSON>
from dataclasses import dataclass
import io

@dataclass
class OCRBox:
    text: str
    confidence: float
    box_coordinates: List[Tuple[int, int]]
    center: Tuple[int, int]

class JCImageProcessor:
    def __init__(self, 
                 ocr_api_url: str = "http://localhost:10002/ocr",
                 block_size: int = 1024,
                 overlap: int = 100,
                 confidence_threshold: float = 0.5):
        self.ocr_api_url = ocr_api_url
        self.block_size = block_size
        self.overlap = overlap
        self.confidence_threshold = confidence_threshold
        self.logger = self._setup_logger()

    def _setup_logger(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)

    def call_ocr_api(self, image: Image.Image) -> List:
        """调用OCR API进行识别"""
        try:
            # 将PIL Image转换为bytes
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='JPEG')
            img_byte_arr = img_byte_arr.getvalue()

            # 准备文件
            files = {'file': ('image.jpg', img_byte_arr, 'image/jpeg')}
            
            # 调用API
            response = requests.post(self.ocr_api_url, files=files)
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            return result.get('result', [])[0] if result.get('result') else []
            
        except Exception as e:
            self.logger.error(f"OCR API调用失败: {e}")
            return []

    def is_valid_symbol(self, text: str) -> bool:
        cleaned_text = re.sub(r'[^A-Za-z0-9]', '', text)
        if not cleaned_text or len(cleaned_text) < 2 or len(cleaned_text) > 20:
            return False
        has_letter = any(c.isalpha() for c in cleaned_text)
        has_digit = any(c.isdigit() for c in cleaned_text)
        return has_letter and has_digit

    def split_image(self, image: Image.Image) -> List[Tuple[Image.Image, Tuple[int, int]]]:
        """将图片分割成块"""
        blocks = []
        width, height = image.size
        
        for y in range(0, height, self.block_size - self.overlap):
            for x in range(0, width, self.block_size - self.overlap):
                # 计算当前块的边界
                right = min(x + self.block_size, width)
                bottom = min(y + self.block_size, height)
                
                # 裁剪图片块
                block = image.crop((x, y, right, bottom))
                blocks.append((block, (x, y)))
                
        self.logger.info(f"图片被分割为 {len(blocks)} 个块")
        return blocks

    def adjust_coordinates(self, 
                         box: List[List[int]], 
                         offset: Tuple[int, int]) -> List[Tuple[int, int]]:
        x_offset, y_offset = offset
        return [(int(point[0] + x_offset), int(point[1] + y_offset)) 
                for point in box]

    def calculate_center(self, box: List[Tuple[int, int]]) -> Tuple[int, int]:
        x = sum(point[0] for point in box) // 4
        y = sum(point[1] for point in box) // 4
        return (x, y)

    def boxes_overlap(self, box1: List[Tuple[int, int]], box2: List[Tuple[int, int]]) -> bool:
        def get_box_bounds(box):
            x_coords = [p[0] for p in box]
            y_coords = [p[1] for p in box]
            return (min(x_coords), max(x_coords), min(y_coords), max(y_coords))

        box1_bounds = get_box_bounds(box1)
        box2_bounds = get_box_bounds(box2)

        x_overlap = not (box1_bounds[1] < box2_bounds[0] or box1_bounds[0] > box2_bounds[1])
        y_overlap = not (box1_bounds[3] < box2_bounds[2] or box1_bounds[2] > box2_bounds[3])

        return x_overlap and y_overlap
    
    def process_image(self, image_path: str) -> List[OCRBox]:
        try:
            # 使用PIL打开图片
            image = Image.open(image_path)
            if image.mode != 'RGB':
                image = image.convert('RGB')

            blocks = self.split_image(image)
            all_results = []

            for block, offset in blocks:
                ocr_result = self.call_ocr_api(block)
                
                if ocr_result:
                    for line in ocr_result:
                        text = line[1][0]
                        confidence = line[1][1]

                        if self.is_valid_symbol(text) and confidence >= self.confidence_threshold:
                            adjusted_box = self.adjust_coordinates(line[0], offset)
                            center = self.calculate_center(adjusted_box)
                            
                            ocr_box = OCRBox(
                                text=text.upper(),
                                confidence=confidence,
                                box_coordinates=adjusted_box,
                                center=center
                            )
                            all_results.append(ocr_box)

                            # 打印识别结果的详细信息
                            self.logger.info(f"识别到文本: {ocr_box.text}, 置信度: {ocr_box.confidence}, 中心: {ocr_box.center}, 坐标: {ocr_box.box_coordinates}")

            # 处理重叠框
            final_results = []
            all_results.sort(key=lambda x: len(x.text), reverse=True)

            for result in all_results:
                overlap_found = False
                for saved_result in final_results:
                    if self.boxes_overlap(result.box_coordinates, saved_result.box_coordinates):
                        overlap_found = True
                        break
                
                if not overlap_found:
                    final_results.append(result)

            self.logger.info(f"原始识别结果数: {len(all_results)}")
            self.logger.info(f"去重后结果数: {len(final_results)}")
            
            return final_results

        except Exception as e:
            self.logger.error(f"处理图片时出错: {e}")
            return []

    # def process_image(self, image_path: str) -> List[OCRBox]:
    #     try:
    #         # 使用PIL打开图片
    #         image = Image.open(image_path)
    #         if image.mode != 'RGB':
    #             image = image.convert('RGB')

    #         blocks = self.split_image(image)
    #         all_results = []

    #         for block, offset in blocks:
    #             ocr_result = self.call_ocr_api(block)
                
    #             if ocr_result:
    #                 for line in ocr_result:
    #                     text = line[1][0]
    #                     confidence = line[1][1]

    #                     if self.is_valid_symbol(text) and confidence >= self.confidence_threshold:
    #                         adjusted_box = self.adjust_coordinates(line[0], offset)
    #                         center = self.calculate_center(adjusted_box)
                            
    #                         all_results.append(OCRBox(
    #                             text=text.upper(),
    #                             confidence=confidence,
    #                             box_coordinates=adjusted_box,
    #                             center=center
    #                         ))

    #         # 处理重叠框
    #         final_results = []
    #         all_results.sort(key=lambda x: len(x.text), reverse=True)

    #         for result in all_results:
    #             overlap_found = False
    #             for saved_result in final_results:
    #                 if self.boxes_overlap(result.box_coordinates, saved_result.box_coordinates):
    #                     overlap_found = True
    #                     break
                
    #             if not overlap_found:
    #                 final_results.append(result)

    #         self.logger.info(f"原始识别结果数: {len(all_results)}")
    #         self.logger.info(f"去重后结果数: {len(final_results)}")
            
    #         return final_results

    #     except Exception as e:
    #         self.logger.error(f"处理图片时出错: {e}")
    #         return []

    def compare_jc_images(self, buzhi_path: str, xiangtu_path: str) -> Dict:
        buzhi_results = self.process_image(buzhi_path)
        xiangtu_results = self.process_image(xiangtu_path)

        buzhi_symbols = [result.text for result in buzhi_results]
        buzhi_counter = {}
        for symbol in set(buzhi_symbols):
            positions = [
                {
                    "center": result.center,
                    "box": result.box_coordinates
                }
                for result in buzhi_results
                if result.text == symbol
            ]
            buzhi_counter[symbol] = {
                "count": buzhi_symbols.count(symbol),
                "positions": positions
            }

        xiangtu_symbols = set(result.text for result in xiangtu_results)

        common_symbols = []
        for symbol in xiangtu_symbols:
            if symbol in buzhi_counter:
                common_symbols.append({
                    "symbol": symbol,
                    "count_in_buzhi": buzhi_counter[symbol]["count"],
                    "positions_in_buzhi": buzhi_counter[symbol]["positions"]
                })

        common_symbols.sort(key=lambda x: x["symbol"])

        return {
            "matches": common_symbols,
            "total_matches": len(common_symbols)
        }

def process_jc_images(jc_buzhi: str, jc_xiangtu: str, api_url: str = "http://localhost:10002/ocr") -> Dict:
    processor = JCImageProcessor(ocr_api_url=api_url)
    return processor.compare_jc_images(jc_buzhi, jc_xiangtu)

if __name__ == "__main__":
    result = process_jc_images("/home/<USER>/py_project/cad_fastapi/backend/app/tasks/buzhi.png", "/home/<USER>/py_project/cad_fastapi/backend/app/tasks/xiangtu.png")
    print(result)