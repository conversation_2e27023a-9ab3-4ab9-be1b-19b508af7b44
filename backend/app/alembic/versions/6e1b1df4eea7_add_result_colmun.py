"""add result colmun

Revision ID: 6e1b1df4eea7
Revises: f6eae6eaa9fd
Create Date: 2024-09-01 05:46:09.964008

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '6e1b1df4eea7'
down_revision = 'f6eae6eaa9fd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('drawing', sa.Column('origin_result', sa.JSON(), nullable=True))
    op.add_column('drawing', sa.Column('finally_result', sa.JSON(), nullable=True))
    op.add_column('recognition_tasks', sa.Column('result', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('recognition_tasks', 'result')
    op.drop_column('drawing', 'finally_result')
    op.drop_column('drawing', 'origin_result')
    # ### end Alembic commands ###
