"""change some config

Revision ID: b6ab44775950
Revises: 0eebeab2a160
Create Date: 2024-11-22 04:37:32.944105

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes

# revision identifiers, used by Alembic.
revision = 'b6ab44775950'
down_revision = '0eebeab2a160'
branch_labels = None
depends_on = None

def upgrade():
    # 创建 ENUM 类型
    op.execute("CREATE TYPE drawingstatus AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED')")
    op.execute("CREATE TYPE recognitiontaskstatus AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED')")
    op.execute("CREATE TYPE userrole AS ENUM ('USER', 'ADMIN', 'SUPERUSER')")
    
    # 转换现有数据到大写，然后转换为 ENUM
    op.execute("ALTER TABLE drawing ALTER COLUMN status TYPE drawingstatus USING UPPER(status)::drawingstatus")
    op.execute("ALTER TABLE recognition_tasks ALTER COLUMN status TYPE recognitiontaskstatus USING UPPER(status)::recognitiontaskstatus")
    op.execute("ALTER TABLE \"user\" ALTER COLUMN role TYPE userrole USING UPPER(role)::userrole")
    
    # 其他列修改
    op.alter_column('drawing', 'original_filename',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('drawing', 'discipline',
               existing_type=sa.VARCHAR(),
               nullable=False)
    
    # 创建索引
    op.create_index(op.f('ix_drawing_latest_recognition_task_id'), 'drawing', ['latest_recognition_task_id'], unique=False)
    op.create_index(op.f('ix_drawing_project_id'), 'drawing', ['project_id'], unique=False)
    op.create_index(op.f('ix_drawing_user_id'), 'drawing', ['user_id'], unique=False)
    op.create_index(op.f('ix_recognition_tasks_celery_task_id'), 'recognition_tasks', ['celery_task_id'], unique=False)
    op.create_index(op.f('ix_recognition_tasks_created_by'), 'recognition_tasks', ['created_by'], unique=False)
    op.create_index(op.f('ix_recognition_tasks_drawing_id'), 'recognition_tasks', ['drawing_id'], unique=False)

def downgrade():
    # 删除索引
    op.drop_index(op.f('ix_recognition_tasks_drawing_id'), table_name='recognition_tasks')
    op.drop_index(op.f('ix_recognition_tasks_created_by'), table_name='recognition_tasks')
    op.drop_index(op.f('ix_recognition_tasks_celery_task_id'), table_name='recognition_tasks')
    op.drop_index(op.f('ix_drawing_user_id'), table_name='drawing')
    op.drop_index(op.f('ix_drawing_project_id'), table_name='drawing')
    op.drop_index(op.f('ix_drawing_latest_recognition_task_id'), table_name='drawing')
    
    # 转换回 VARCHAR，并转换为小写
    op.execute('ALTER TABLE drawing ALTER COLUMN status TYPE VARCHAR USING LOWER(status::TEXT)')
    op.execute('ALTER TABLE recognition_tasks ALTER COLUMN status TYPE VARCHAR USING LOWER(status::TEXT)')
    op.execute('ALTER TABLE "user" ALTER COLUMN role TYPE VARCHAR USING LOWER(role::TEXT)')
    
    # 修改其他列
    op.alter_column('drawing', 'discipline',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('drawing', 'original_filename',
               existing_type=sa.VARCHAR(),
               nullable=True)
               
    # 删除 ENUM 类型
    op.execute("DROP TYPE drawingstatus")
    op.execute("DROP TYPE recognitiontaskstatus")
    op.execute("DROP TYPE userrole")