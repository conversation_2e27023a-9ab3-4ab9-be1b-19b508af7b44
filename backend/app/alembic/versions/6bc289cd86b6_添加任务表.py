"""添加任务表

Revision ID: 6bc289cd86b6
Revises: 5a0b74a57333
Create Date: 2024-08-31 12:03:35.468896

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '6bc289cd86b6'
down_revision = '5a0b74a57333'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('recognition_tasks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('drawing_id', sa.Integer(), nullable=False),
    sa.Column('status', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('celery_task_id', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.<PERSON>umn('completed_at', sa.DateTime(), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['created_by'], ['user.id'], ),
    sa.ForeignKeyConstraint(['drawing_id'], ['drawing.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_recognition_tasks_status'), 'recognition_tasks', ['status'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_recognition_tasks_status'), table_name='recognition_tasks')
    op.drop_table('recognition_tasks')
    # ### end Alembic commands ###
