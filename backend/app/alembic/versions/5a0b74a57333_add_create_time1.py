"""add create time1

Revision ID: 5a0b74a57333
Revises: 67549a4dd1a9
Create Date: 2024-06-18 11:31:21.525776

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '5a0b74a57333'
down_revision = '67549a4dd1a9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('drawing', sa.Column('uploaded_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('drawing', 'uploaded_at')
    # ### end Alembic commands ###
