"""user Add discipline

Revision ID: 9009d90d3269
Revises: 344c28544272
Create Date: 2024-06-17 05:55:08.275844

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '9009d90d3269'
down_revision = '344c28544272'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('discipline',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('drawing', sa.Column('file_name', sqlmodel.sql.sqltypes.AutoString(), nullable=False))
    op.add_column('drawing', sa.Column('file_path', sqlmodel.sql.sqltypes.AutoString(), nullable=False))
    op.add_column('drawing', sa.Column('category', sqlmodel.sql.sqltypes.AutoString(), nullable=False))
    op.add_column('drawing', sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.add_column('drawing', sa.Column('uploaded_at', sa.DateTime(), nullable=False))
    op.add_column('drawing', sa.Column('status', sqlmodel.sql.sqltypes.AutoString(), nullable=False))
    op.add_column('drawing', sa.Column('user_id', sa.Integer(), nullable=False))
    op.add_column('drawing', sa.Column('discipline_id', sa.Integer(), nullable=False))
    op.drop_constraint('drawing_owner_id_fkey', 'drawing', type_='foreignkey')
    op.create_foreign_key(None, 'drawing', 'discipline', ['discipline_id'], ['id'])
    op.create_foreign_key(None, 'drawing', 'user', ['user_id'], ['id'])
    op.drop_column('drawing', 'owner_id')
    op.drop_column('drawing', 'name')
    op.drop_column('drawing', 'filepath')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('drawing', sa.Column('filepath', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('drawing', sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('drawing', sa.Column('owner_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'drawing', type_='foreignkey')
    op.drop_constraint(None, 'drawing', type_='foreignkey')
    op.create_foreign_key('drawing_owner_id_fkey', 'drawing', 'user', ['owner_id'], ['id'])
    op.drop_column('drawing', 'discipline_id')
    op.drop_column('drawing', 'user_id')
    op.drop_column('drawing', 'status')
    op.drop_column('drawing', 'uploaded_at')
    op.drop_column('drawing', 'description')
    op.drop_column('drawing', 'category')
    op.drop_column('drawing', 'file_path')
    op.drop_column('drawing', 'file_name')
    op.drop_table('discipline')
    # ### end Alembic commands ###
