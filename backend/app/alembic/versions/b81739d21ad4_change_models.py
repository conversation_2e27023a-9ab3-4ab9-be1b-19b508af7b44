"""change models

Revision ID: b81739d21ad4
Revises: c10fb456bf1b
Create Date: 2024-06-08 03:51:33.863449

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'b81739d21ad4'
down_revision = 'c10fb456bf1b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
