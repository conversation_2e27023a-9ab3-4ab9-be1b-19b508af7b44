"""user Add nickname role

Revision ID: 344c28544272
Revises: b81739d21ad4
Create Date: 2024-06-15 10:21:41.476567

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '344c28544272'
down_revision = 'b81739d21ad4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('quantityresult',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('drawing_id', sa.Integer(), nullable=False),
    sa.Column('component_type', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('quantity', sa.Float(), nullable=False),
    sa.Column('unit_price', sa.Float(), nullable=False),
    sa.Column('total_price', sa.Float(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['drawing_id'], ['drawing.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('drawing_id')
    )
    op.add_column('component', sa.Column('drawing_id', sa.Integer(), nullable=True))
    op.drop_constraint('component_project_id_fkey', 'component', type_='foreignkey')
    op.create_foreign_key(None, 'component', 'drawing', ['drawing_id'], ['id'])
    op.drop_column('component', 'project_id')
    op.add_column('drawing', sa.Column('owner_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'drawing', 'user', ['owner_id'], ['id'])
    op.add_column('user', sa.Column('nickname', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.add_column('user', sa.Column('role', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'role')
    op.drop_column('user', 'nickname')
    op.drop_constraint(None, 'drawing', type_='foreignkey')
    op.drop_column('drawing', 'owner_id')
    op.add_column('component', sa.Column('project_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'component', type_='foreignkey')
    op.create_foreign_key('component_project_id_fkey', 'component', 'project', ['project_id'], ['id'])
    op.drop_column('component', 'drawing_id')
    op.drop_table('quantityresult')
    # ### end Alembic commands ###
