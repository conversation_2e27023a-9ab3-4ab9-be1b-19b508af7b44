"""jia hui qu

Revision ID: b5372d52d187
Revises: a6c081f013ff
Create Date: 2024-12-02 09:48:12.634601

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'b5372d52d187'
down_revision = 'a6c081f013ff'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('drawing', sa.Column('latest_recognition_task_id', sa.Integer(), nullable=True))
    op.create_index(op.f('ix_drawing_latest_recognition_task_id'), 'drawing', ['latest_recognition_task_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_drawing_latest_recognition_task_id'), table_name='drawing')
    op.drop_column('drawing', 'latest_recognition_task_id')
    # ### end Alembic commands ###
