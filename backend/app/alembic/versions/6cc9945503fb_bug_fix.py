"""bug fix

Revision ID: 6cc9945503fb
Revises: 69e38bbb29be
Create Date: 2024-09-02 12:04:45.811520

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '6cc9945503fb'
down_revision = '69e38bbb29be'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
