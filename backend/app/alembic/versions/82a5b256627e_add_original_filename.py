"""add original_filename

Revision ID: 82a5b256627e
Revises: 039939d26801
Create Date: 2024-06-18 04:28:14.358371

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '82a5b256627e'
down_revision = '039939d26801'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('drawing', sa.Column('original_filename', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('drawing', 'original_filename')
    # ### end Alembic commands ###
