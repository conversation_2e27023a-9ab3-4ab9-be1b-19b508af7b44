"""Remove Discipline model and related constraints

Revision ID: 9c5f43079469
Revises: ac1656dd8bd0
Create Date: 2024-09-29 03:36:08.720412

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '9c5f43079469'
down_revision = 'ac1656dd8bd0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('discipline')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('discipline',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='discipline_pkey')
    )
    # ### end Alembic commands ###
