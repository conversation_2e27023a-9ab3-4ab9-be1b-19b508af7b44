"""fix drawing_Set

Revision ID: 9da5fb394419
Revises: b6ab44775950
Create Date: 2024-11-22 13:29:33.129280

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql
from sqlalchemy import Enum

# revision identifiers, used by Alembic.
revision = '9da5fb394419'
down_revision = 'b6ab44775950'
branch_labels = None
depends_on = None

# 创建 DrawingType 枚举
drawing_type_enum = Enum(
    'FOUNDATION_LAYOUT',
    'FOUNDATION_DETAIL_PLAN',
    'FOUNDATION_SECTION',
    name='drawingtype'
)

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    
    # 先创建枚举类型
    drawing_type_enum.create(op.get_bind(), checkfirst=True)
    
    # 创建套图表
    op.create_table('drawing_sets',
        sa.Column('name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column('project_id', sa.Integer(), nullable=False),
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('required_types', sa.JSON(), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['user.id'], ),
        sa.ForeignKeyConstraint(['project_id'], ['project.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 添加图纸相关列
    op.add_column('drawing', sa.Column('drawing_set_id', sa.Integer(), nullable=True))
    op.add_column('drawing', sa.Column('drawing_type', drawing_type_enum, nullable=True))
    op.create_index(op.f('ix_drawing_drawing_set_id'), 'drawing', ['drawing_set_id'], unique=False)
    op.create_foreign_key(None, 'drawing', 'drawing_sets', ['drawing_set_id'], ['id'])
    
    # 修改用户角色列
    op.alter_column('user', 'role',
               existing_type=postgresql.ENUM('USER', 'ADMIN', 'SUPERUSER', name='userrole'),
               nullable=False)


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('user', 'role',
               existing_type=postgresql.ENUM('USER', 'ADMIN', 'SUPERUSER', name='userrole'),
               nullable=True)
    op.drop_constraint(None, 'drawing', type_='foreignkey')
    op.drop_index(op.f('ix_drawing_drawing_set_id'), table_name='drawing')
    op.drop_column('drawing', 'drawing_type')
    op.drop_column('drawing', 'drawing_set_id')
    op.drop_table('drawing_sets')
    
    # 删除枚举类型
    drawing_type_enum.drop(op.get_bind(), checkfirst=True)
    # ### end Alembic commands ###