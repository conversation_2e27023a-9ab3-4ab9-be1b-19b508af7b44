"""delete dis

Revision ID: a33ee1c7eddc
Revises: b715d09e5d91
Create Date: 2024-09-29 03:13:32.614468

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'a33ee1c7eddc'
down_revision = 'b715d09e5d91'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('drawing', sa.Column('discipline', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.drop_constraint('drawing_discipline_id_fkey', 'drawing', type_='foreignkey')
    op.drop_column('drawing', 'discipline_id')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('drawing', sa.Column('discipline_id', sa.INTEGER(), autoincrement=False, nullable=False))
    op.create_foreign_key('drawing_discipline_id_fkey', 'drawing', 'discipline', ['discipline_id'], ['id'])
    op.drop_column('drawing', 'discipline')
    # ### end Alembic commands ###
