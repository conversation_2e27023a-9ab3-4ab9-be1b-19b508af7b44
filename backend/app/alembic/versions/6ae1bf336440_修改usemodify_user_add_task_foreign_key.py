"""修改usemodify_user_add_task_foreign_key

Revision ID: 6ae1bf336440
Revises: 6bc289cd86b6
Create Date: 2024-08-31 13:29:57.907008

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '6ae1bf336440'
down_revision = '6bc289cd86b6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
