"""处理chu li xun huan yin yong

Revision ID: a6c081f013ff
Revises: 220db606e786
Create Date: 2024-12-02 09:35:23.432864

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'a6c081f013ff'
down_revision = '220db606e786'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_drawing_latest_recognition_task_id', table_name='drawing')
    op.drop_constraint('drawing_latest_recognition_task_id_fkey', 'drawing', type_='foreignkey')
    op.drop_column('drawing', 'latest_recognition_task_id')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('drawing', sa.Column('latest_recognition_task_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key('drawing_latest_recognition_task_id_fkey', 'drawing', 'recognition_tasks', ['latest_recognition_task_id'], ['id'])
    op.create_index('ix_drawing_latest_recognition_task_id', 'drawing', ['latest_recognition_task_id'], unique=False)
    # ### end Alembic commands ###
