"""add url

Revision ID: b715d09e5d91
Revises: 6cc9945503fb
Create Date: 2024-09-10 02:22:35.830118

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'b715d09e5d91'
down_revision = '6cc9945503fb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('drawing', sa.Column('file_url', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('drawing', 'file_url')
    # ### end Alembic commands ###
