"""add create time

Revision ID: 67549a4dd1a9
Revises: 82a5b256627e
Create Date: 2024-06-18 11:30:44.875225

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '67549a4dd1a9'
down_revision = '82a5b256627e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('drawing', 'uploaded_at')
    op.add_column('project', sa.Column('created_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('project', 'created_at')
    op.add_column('drawing', sa.Column('uploaded_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
