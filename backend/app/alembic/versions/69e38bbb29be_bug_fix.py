"""bug fix

Revision ID: 69e38bbb29be
Revises: 6e1b1df4eea7
Create Date: 2024-09-02 11:52:52.095056

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '69e38bbb29be'
down_revision = '6e1b1df4eea7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('drawing', sa.Column('latest_recognition_task_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'drawing', 'recognition_tasks', ['latest_recognition_task_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'drawing', type_='foreignkey')
    op.drop_column('drawing', 'latest_recognition_task_id')
    # ### end Alembic commands ###
