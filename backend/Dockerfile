# 使用 CUDA 基础镜像
FROM nvidia/cuda:11.8.0-cudnn8-runtime-ubuntu22.04

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONPATH=/app \
    DEBIAN_FRONTEND=noninteractive \
    TZ=Asia/Shanghai

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    python3.10 \
    python3-pip \
    python3.10-dev \
    build-essential \
    git \
    wget \
    curl \
    libgl1-mesa-glx \
    libglib2.0-0 \
    poppler-utils \
    nginx \
    supervisor \
    && rm -rf /var/lib/apt/lists/* \
    && ln -sf /usr/bin/python3.10 /usr/bin/python

WORKDIR /app/

# 创建启动脚本
RUN echo '#!/bin/bash\n\
HOST=${HOST:-0.0.0.0}\n\
PORT=${PORT:-80}\n\
LOG_LEVEL=${LOG_LEVEL:-info}\n\
\n\
exec uvicorn app.main:app --host $HOST --port $PORT --reload --log-level $LOG_LEVEL\n\
' > /start-reload.sh \
    && chmod +x /start-reload.sh

# 创建生产环境启动脚本
RUN echo '#!/bin/bash\n\
HOST=${HOST:-0.0.0.0}\n\
PORT=${PORT:-80}\n\
WORKERS=${WORKERS:-4}\n\
LOG_LEVEL=${LOG_LEVEL:-info}\n\
\n\
exec gunicorn app.main:app -w $WORKERS -k uvicorn.workers.UvicornWorker -b $HOST:$PORT --log-level $LOG_LEVEL\n\
' > /start.sh \
    && chmod +x /start.sh

# Copy the requirements file into the container
COPY ./requirements.txt /app/

# Install dependencies using pip and Tsinghua mirror
RUN pip install --upgrade pip \
    && pip install -i https://mirrors.aliyun.com/pypi/simple --no-cache-dir --progress-bar off -r requirements.txt

# 安装最新版ultralytics（YOLOv9）
# RUN pip install --no-cache-dir git+https://github.com/ultralytics/ultralytics.git
RUN pip install --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple git+https://github.com/ultralytics/ultralytics.git

ENV PYTHONPATH=/app

COPY ./scripts/ /app/

COPY ./alembic.ini /app/

COPY ./prestart.sh /app/

COPY ./tests-start.sh /app/

COPY ./yolov9 /app/yolov9

COPY ./asset/Arial.Unicode.ttf /root/.config/Ultralytics/

COPY ./app /app/app

# 设置权限
RUN chmod +x /app/prestart.sh /app/tests-start.sh

# 设置默认命令
CMD ["/start-reload.sh"]
