## 项目概述
CAD图纸识别和算量

## 系统架构

### 技术栈
- 前端：Vue3（让李老师联系吴瑾）
- 后端：FastAPI + Celery
- 数据库：PostgreSQL + Adminer
- 容器化：Docker + Docker Compose
- 图像识别：YOLOv9
- OCR服务：PaddleOCR + surya OCR（有时候需要试试哪个效果好）

### 服务组件
- nginx: 反向代理服务器
- backend: 主要后端服务
- celery_worker: 异步任务处理
- celery_beat: 定时任务调度
- db: PostgreSQL数据库
- adminer: 数据库管理界面
- redis: 消息队列和缓存
- ocr_service: surya OCR服务
- paddle_ocr: PaddleOCR服务

## 部署信息
- 地址：106服务器
- 代码路径：`/data/wjw_workspace/py_project/cad_fastapi`
- API文档地址：http://172.25.168.106:8091/api/docs
- 数据库登录地址：http://172.25.168.106:18080

### 部署命令
```bash
# 修改环境依赖后重新构建并启动
docker-compose -f docker-compose.106.yml up -d --build

# 无环境依赖修改时重启
docker-compose -f docker-compose.106.yml restart

# 查看所有容器日志
docker-compose logs -f

# 查看单个容器日志（例如后端服务）
docker logs -f cad_fastapi
```

## 核心模块说明

### 1. yolov9模型管理
- 模型文件位置：`backend/yolov9/v9model/`
- 模型映射配置：`backend/app/core/*mapping.json`
- 说明：新增或更新模型时，需要同时更新模型文件和映射配置

### 2. 图纸识别模块
- 入口文件：`backend/app/api/routes/drawings.py`
- 识别任务实现：`backend/app/tasks/recognition/`
- 核心文件：
  - `detail_strategy.py`: 基础详图识别实现
  - `strategy_factory.py`: 识别策略工厂类
- 新增识别类型步骤：
  1. 在recognition目录下创建新的策略类
  2. 在strategy_factory.py中注册新策略
  3. 更新相应的API路由

### 3. 工程量计算模块
- 代码位置：`backend/app/calculations/`
- 核心文件：`volume_calculator.py`
- 新增算量逻辑步骤：
  1. 在calculations目录下创建新的计算器类
  2. 实现相应的计算方法
  3. 在API中集成新的计算逻辑

## 环境变量配置
项目使用.env文件管理环境变量，主要包含：
- 数据库配置
- Redis配置
- OCR服务配置
- GPU相关配置

## 现有限制
- 上传需要上传方向为正的jpg格式图纸(pdf,png等由于转换过程可能出现方向错误)