name: Issue Manager

on:
  schedule:
    - cron: "0 0 * * *"
  issue_comment:
    types:
      - created
      - edited
  issues:
    types:
      - labeled

jobs:
  issue-manager:
    runs-on: ubuntu-latest
    steps:
      - uses: tiangolo/issue-manager@0.5.0
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          config: >
            {
              "answered": {
                "users": ["tiangolo"],
                "delay": 864000,
                "message": "Assuming the original issue was solved, it will be automatically closed now. But feel free to add more comments or create new issues."
              }
            }
